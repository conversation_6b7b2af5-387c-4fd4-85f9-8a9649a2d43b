#!/bin/bash

# Test script for synapse-agent
set -e

AGENT_PORT=${AGENT_PORT:-8080}
METRICS_PORT=${METRICS_PORT:-8081}
AGENT_HOST=${AGENT_HOST:-localhost}

echo "Testing synapse-agent on ${AGENT_HOST}:${AGENT_PORT}"

# Function to test HTTP endpoint
test_endpoint() {
    local path=$1
    local expected_status=${2:-200}
    local description=$3
    
    echo -n "Testing ${description} (${path})... "
    
    status=$(curl -s -o /dev/null -w "%{http_code}" "http://${AGENT_HOST}:${AGENT_PORT}${path}")
    
    if [ "$status" = "$expected_status" ]; then
        echo "✓ PASS (HTTP $status)"
    else
        echo "✗ FAIL (Expected HTTP $expected_status, got HTTP $status)"
        exit 1
    fi
}

# Function to test metrics endpoint
test_metrics() {
    echo -n "Testing metrics endpoint... "
    
    metrics=$(curl -s "http://${AGENT_HOST}:${METRICS_PORT}/metrics")
    
    if echo "$metrics" | grep -q "synapse_agent_info"; then
        echo "✓ PASS (Found synapse_agent_info metric)"
    else
        echo "✗ FAIL (synapse_agent_info metric not found)"
        exit 1
    fi
    
    if echo "$metrics" | grep -q "synapse_agent_requests_total"; then
        echo "✓ PASS (Found synapse_agent_requests_total metric)"
    else
        echo "✗ FAIL (synapse_agent_requests_total metric not found)"
        exit 1
    fi
}

# Function to test version endpoint JSON response
test_version_json() {
    echo -n "Testing version JSON response... "
    
    response=$(curl -s "http://${AGENT_HOST}:${AGENT_PORT}/version")
    
    if echo "$response" | jq -e '.version' > /dev/null 2>&1; then
        echo "✓ PASS (Valid JSON with version field)"
    else
        echo "✗ FAIL (Invalid JSON or missing version field)"
        echo "Response: $response"
        exit 1
    fi
}

# Run tests
echo "=== Synapse Agent Test Suite ==="
echo

test_endpoint "/" 200 "root endpoint"
test_endpoint "/health" 200 "health check"
test_endpoint "/ready" 200 "readiness check"
test_endpoint "/version" 200 "version endpoint"
test_endpoint "/nonexistent" 404 "non-existent endpoint"

test_version_json
test_metrics

echo
echo "=== All tests passed! ==="
echo "Agent is functioning correctly."
