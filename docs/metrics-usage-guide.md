# Synapse Metrics Usage Guide

## Overview

This guide explains how to use the Prometheus metrics provided by Synapse Controller to monitor network health, set up alerting, and create dashboards.

## Quick Start

### 1. Enable Metrics Collection

Metrics are enabled by default. To configure:

```yaml
# In your ProbeConfig
enableMetrics: true
metricsAggregationInterval: 60s
```

### 2. Access Metrics Endpoint

The controller exposes metrics on the standard `/metrics` endpoint:

```bash
curl http://controller-service:8080/metrics
```

### 3. Configure Prometheus

Add the following to your Prometheus configuration:

```yaml
scrape_configs:
  - job_name: 'synapse-controller'
    static_configs:
      - targets: ['synapse-controller:8080']
    scrape_interval: 30s
    metrics_path: /metrics
```

## Available Metrics

### Node-Level Metrics

#### Network Health Status

```prometheus
# HELP synapse_node_network_health Node network health status (1=healthy, 0=unhealthy)
# TYPE synapse_node_network_health gauge
synapse_node_network_health{node="worker-1",status="healthy"} 1
synapse_node_network_health{node="worker-1",status="unhealthy"} 0
```

#### Network Latency

```prometheus
# HELP synapse_node_network_latency_seconds Node network latency statistics in seconds
# TYPE synapse_node_network_latency_seconds gauge
synapse_node_network_latency_seconds{node="worker-1",quantile="avg"} 0.025
synapse_node_network_latency_seconds{node="worker-1",quantile="p95"} 0.045
synapse_node_network_latency_seconds{node="worker-1",quantile="p99"} 0.055
```

#### Packet Loss Rate

```prometheus
# HELP synapse_node_network_loss_rate Node network packet loss rate (0.0-1.0)
# TYPE synapse_node_network_loss_rate gauge
synapse_node_network_loss_rate{node="worker-1"} 0.02
```

### Probe Statistics

#### Probe Requests

```prometheus
# HELP synapse_node_probe_requests_total Total number of probe requests
# TYPE synapse_node_probe_requests_total counter
synapse_node_probe_requests_total{node="worker-1",status="success"} 150
synapse_node_probe_requests_total{node="worker-1",status="failed"} 5
```

#### Probe Duration and Timing

```prometheus
# HELP synapse_node_probe_duration_seconds Duration of the last probe in seconds
# TYPE synapse_node_probe_duration_seconds gauge
synapse_node_probe_duration_seconds{node="worker-1"} 180.5

# HELP synapse_node_probe_last_time_seconds Timestamp of the last probe (Unix timestamp)
# TYPE synapse_node_probe_last_time_seconds gauge
synapse_node_probe_last_time_seconds{node="worker-1"} 1640995200
```

### Controller Status

#### Active Probes

```prometheus
# HELP synapse_controller_active_probes Number of currently active probe tasks
# TYPE synapse_controller_active_probes gauge
synapse_controller_active_probes{node="worker-1"} 1
```

#### Probe Errors

```prometheus
# HELP synapse_controller_probe_errors_total Total number of probe errors
# TYPE synapse_controller_probe_errors_total counter
synapse_controller_probe_errors_total{node="worker-1",error_type="timeout"} 2
synapse_controller_probe_errors_total{node="worker-1",error_type="connection"} 1
```

### Cluster-Level Metrics

#### Cluster Health Summary

```prometheus
# HELP synapse_cluster_healthy_nodes_total Total number of healthy nodes in the cluster
# TYPE synapse_cluster_healthy_nodes_total gauge
synapse_cluster_healthy_nodes_total 8

# HELP synapse_cluster_unhealthy_nodes_total Total number of unhealthy nodes in the cluster
# TYPE synapse_cluster_unhealthy_nodes_total gauge
synapse_cluster_unhealthy_nodes_total 2
```

## Common Queries

### Health Monitoring

```promql
# Current healthy node percentage
synapse_cluster_healthy_nodes_total / (synapse_cluster_healthy_nodes_total + synapse_cluster_unhealthy_nodes_total) * 100

# Nodes with high latency (P99 > 50ms)
synapse_node_network_latency_seconds{quantile="p99"} > 0.05

# Nodes with packet loss
synapse_node_network_loss_rate > 0

# Probe success rate by node
rate(synapse_node_probe_requests_total{status="success"}[5m]) / rate(synapse_node_probe_requests_total[5m])
```

### Performance Analysis

```promql
# Average latency trend over time
avg(synapse_node_network_latency_seconds{quantile="avg"})

# Top 5 nodes with highest P99 latency
topk(5, synapse_node_network_latency_seconds{quantile="p99"})

# Probe error rate by error type
rate(synapse_controller_probe_errors_total[5m])
```

### Capacity Planning

```promql
# Active probe count trend
sum(synapse_controller_active_probes)

# Controller restart frequency
rate(synapse_controller_restarts_total[1h])
```

## Setting Up Alerts

### Import Alert Rules

1. Save the provided `prometheus-alerts.yaml` to your Prometheus configuration directory
2. Add to your Prometheus configuration:

```yaml
rule_files:
  - "synapse-alerts.yaml"
```

3. Reload Prometheus configuration

### Key Alert Rules

- **SynapseNodeNetworkUnhealthy**: Node network issues
- **SynapseHighNetworkLatency**: Latency above thresholds
- **SynapseClusterNetworkDegraded**: Cluster-wide issues
- **SynapseHighProbeErrorRate**: Controller problems

## Creating Dashboards

### Import Grafana Dashboard

1. Import the provided `grafana-dashboard.json`
2. Configure data source to point to your Prometheus instance
3. Customize panels as needed

### Key Dashboard Panels

- **Cluster Health Overview**: High-level status
- **Network Latency Trends**: Performance over time
- **Node Health Table**: Detailed node status
- **Probe Statistics**: Controller performance

## Troubleshooting

### No Metrics Available

1. Check if metrics are enabled in configuration
2. Verify controller is running and accessible
3. Check Prometheus scrape configuration
4. Review controller logs for errors

### Missing Node Data

1. Verify synapse-agent pods are running on nodes
2. Check if probes are completing successfully
3. Review node annotations for probe results

### High Memory Usage

1. Monitor metrics cardinality
2. Consider reducing metrics retention
3. Adjust aggregation intervals

## Best Practices

### Monitoring Strategy

1. **Set up tiered alerting**: Info → Warning → Critical
2. **Monitor trends**: Use rate() and increase() functions
3. **Create runbooks**: Document response procedures
4. **Regular reviews**: Analyze metrics for patterns

### Performance Optimization

1. **Appropriate scrape intervals**: Balance freshness vs. load
2. **Efficient queries**: Use recording rules for complex calculations
3. **Retention policies**: Balance storage vs. historical data needs

### Security Considerations

1. **Secure metrics endpoint**: Use TLS and authentication
2. **Network policies**: Restrict access to metrics
3. **RBAC**: Limit Prometheus service account permissions

## Integration Examples

### With AlertManager

```yaml
# alertmanager.yml
route:
  group_by: ['alertname', 'cluster', 'service']
  routes:
    - match:
        component: synapse
      receiver: 'synapse-team'

receivers:
  - name: 'synapse-team'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK'
        channel: '#network-alerts'
        title: 'Synapse Network Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
```

### With Custom Applications

```python
# Python example using prometheus_client
from prometheus_client.parser import text_string_to_metric_families
import requests

def get_synapse_metrics():
    response = requests.get('http://synapse-controller:8080/metrics')
    metrics = {}
    
    for family in text_string_to_metric_families(response.text):
        if family.name.startswith('synapse_'):
            metrics[family.name] = family
    
    return metrics
```

## Support

For issues or questions:

1. Check controller logs
2. Review Prometheus targets status
3. Consult the troubleshooting section
4. Open an issue in the project repository
