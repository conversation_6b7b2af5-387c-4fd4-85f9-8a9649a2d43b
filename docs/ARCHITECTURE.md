# Synapse Architecture Diagram

This document contains a Mermaid diagram illustrating the architecture of the Synapse network prober.

```mermaid
graph TD
    subgraph "Kubernetes Cluster"
        K8sAPIServer["Kubernetes API Server"]
        Controller["synapse-controller (Deployment)"]

        subgraph "Worker Node 1"
            Agent1["synapse-agent Pod"]
        end

        subgraph "Worker Node N (New)"
            AgentN["synapse-agent Pod"]
        end
    end

    Admin[Admin/CI-CD] -- "kubectl apply" --> K8sAPIServer

    K8sAPIServer -- "DaemonSet ensures Agent Pods run on Nodes" --> Agent1
    K8sAPIServer -- " " --> AgentN

    Controller -- "1. Watches (LIST/WATCH) for new Agent Pods" --> K8sAPIServer
    
    subgraph "Probing Cycle"
        direction LR
        Controller -- "2. Starts Probe (HTTP Ping)" --> AgentN
        AgentN -- "3. Responds (HTTP Pong)" --> Controller
    end

    Controller -- "4. Analyzes result & Patches Node Annotation" --> K8sAPIServer

    subgraph "Result"
        K8sScheduler["Kubernetes Scheduler"]
        K8sAPIServer -- "Node 'node-n' is now annotated<br/>'synapse.io/network-status: healthy'" --> K8sScheduler
        K8sScheduler -- "Can schedule Business Pods to healthy nodes" --> AgentN
    end

```

### Diagram Legend

-   **Admin/CI-CD:** The user or automated system that deploys the Synapse manifests.
-   **Kubernetes API Server:** The central control plane of the cluster.
-   **synapse-controller:** The core logic that orchestrates the network probes.
-   **synapse-agent Pod:** The lightweight network target running on each node.
-   **Probing Cycle:** The core interaction loop where the controller sends a ping to the agent and receives a pong.
-   **Result:** The outcome of a successful probe, where the node is annotated, allowing the Kubernetes Scheduler to place new application pods on it.
