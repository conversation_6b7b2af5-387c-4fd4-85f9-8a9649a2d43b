# 先进的网络诊断平台

本文档阐述了一个构建于 Synapse Controller-Agent 架构之上的综合性网络诊断平台的设计。它超越了简单的连通性检查，旨在诊断贯穿整个 Kubernetes 网络栈（从 CNI 到 Ingress）的复杂问题。此处描述的策略优先考虑可靠性、低侵入性，并专注于对最终效果的“黑盒”测试，而非对实现细节的“白盒”检查。

---

## 1. 服务抽象层诊断

该测试套件专注于 Kubernetes 的服务（Service）抽象，确保由 `kube-proxy` 管理的流量路由和由 `CoreDNS` 管理的 DNS 解析功能正常。

### 1.1. ClusterIP 可达性

- **目标:** 验证集群中的任何 Pod 都能通过服务的稳定 `ClusterIP` 访问到该服务。
- **解决的问题:** 检测导致流量无法到达服务后端的、已损坏或过时的 `kube-proxy` 规则（基于 iptables/IPVS）。
- **优化策略:**
    1. **目标选择:** Controller 将一个稳定存在的服务（例如 `kube-system` 命名空间下的 `kube-dns`）或一个由 Synapse 部署的、长期运行的专用“金丝雀”应用作为探测目标。这避免了为每次测试动态创建资源所带来的开销和风险。
    2. **执行:** Controller 指示一个随机的 `synapse-agent` 对目标的 `http://<ClusterIP>:<Port>` 发起一次 HTTP GET 请求（或 TCP 连接测试）。
    3. **验证:** 如果 Agent 收到成功的响应（例如 HTTP 200 OK），则测试通过。

### 1.2. NodePort 可达性

- **目标:** 验证在集群内部，可以通过 `NodePort` 从其他节点访问到服务。
- **解决的问题:** 定位那些阻止了到特定 `NodePort` 的跨节点流量的主机防火墙、云安全组或网络 ACL 的错误配置。
- **优化策略:**
    1. **目标选择:** 使用一个专用的、类型为 `NodePort` 的金丝雀服务。
    2. **执行:** Controller 指示位于节点 A 上的 Agent 连接到节点 B 上的 `NodePort` (`http://<NodeB_IP>:<NodePort>`)。
    3. **验证:** 连接成功即表示测试通过，确认了跨节点数据路径是通畅的。

### 1.3. Endpoints 同步

- **目标:** 确保一个服务的 `Endpoints` 对象能够正确、及时地与其健康的后端 Pod 集合同步。
- **解决的问题:** 检测因 Endpoint Controller 的延迟或失败，导致流量被发送到不存在或正在终止的 Pod（即“流量黑洞”）的情况。
- **优化策略:**
    1. **基于 API 的比较:** Controller 直接比较从 Kubernetes API 获取的两个列表：
        - 列表 A: `Endpoints` 对象中的 IP 地址。
        - 列表 B: 所有处于 `Running` 状态且匹配服务标签选择器的 Pod 的 IP 地址。
    2. **鲁棒性:** 为避免因正常的调度延迟而产生误报，检查会忽略处于 `Terminating` 状态的 Pod，并引入重试机制。只有当两个列表在一段确定的时间窗口内（例如 15 秒）持续不一致时，才会触发警报。

### 1.4. Headless Service DNS 解析

- **目标:** 验证 Headless Service 的 DNS 名称能够正确解析为其所有后端 Pod 的 IP 地址。
- **解决的问题:** 捕获导致有状态应用无法发现其对等点的 DNS 缓存问题或 `CoreDNS` 错误配置。
- **优化策略:**
    1. **缓存穿透:** `synapse-agent` 对一个金丝雀 Headless Service 执行 DNS 查询。为绕过潜在的缓存，Agent 可以直接查询 `kube-dns` 服务的 IP，或在每次查询时为域名附加一个随机前缀。
    2. **验证:** Controller 将 DNS 查询返回的 IP 列表与从 Kubernetes API 获取的真实后端 Pod IP 列表进行比较。

---

## 2. 南北向流量诊断

这些测试用于验证进出集群的流量（Ingress 和 Egress）。

### 2.1. Ingress 路径验证

- **目标:** 验证从 Ingress Controller 到后端 Pod 的流量路径。
- **解决的问题:** 精准定位 `Ingress` 规则配置错误、TLS 卸载问题，或 Ingress、Service 与 Pod 之间的连接中断。
- **优化策略:**
  - **内部测试 (默认):** 一个位于集群*内部*的 Agent 向 Ingress Controller 的内部 IP 发送 HTTP 请求，并提供正确的 `Host` 头。这有效地测试了 `Ingress Controller -> Service -> Pod` 这一段路径。
  - **外部测试 (可选扩展):** 为了实现真正的端到端验证，可以部署一个可选的、位于集群外部的探测器。该组件将测试包括公网 DNS 解析、外部负载均衡器和云防火墙在内的完整路径。

### 2.2. Egress 连通性与 NAT 网关

- **目标:** 验证 Pod 能否访问外部服务，并识别其公网源 IP。
- **解决的问题:** 检测限制性的 Egress 防火墙规则、NAT 网关故障或公网 IP 被列入黑名单等问题。
- **优化策略:**
    1. **冗余性:** `synapse-agent` 尝试连接多个、冗余的公共“IP 回显”服务列表（例如 `https://api.ipify.org`, `https://ifconfig.me`）。
    2. **仲裁:** 如果列表中有足够数量（例如，3个中的2个）的外部服务成功响应，则测试被认为是成功的。这可以防止因单个外部服务故障而导致的误报。
    3. **可配置性:** 外部端点列表是用户可配置的，允许他们指定自己的高可用探测目标。

---

## 3. 底层基础设施与高级功能

这些诊断旨在检查底层 CNI 和高级网络层（如服务网格）的健康状况。

### 3.1. CNI 特定健康检查 (以 Calico 为例)

- **目标:** 检查那些无法通过标准连通性测试发现的、CNI 特定组件的健康状况。
- **解决的问题:** 识别底层 CNI 机制的故障，例如 Calico 中的 BGP 对等连接中断。
- **优化策略:**
    1. **API 优先方法:** Controller 直接与 CNI 的自定义资源定义 (CRD) 进行交互，而不是解析 `calicoctl` 等命令行工具的输出。
    2. **执行:** 对于 Calico，Controller 将通过 Kubernetes API 列出 `BGPPeer` 资源，并检查其 `.status` 字段，以确保所有对等会话都处于 `Established` (已建立) 状态。这种方法健壮且不受 CLI 输出格式变化的影响。

### 3.2. 服务网格健康检查 (以 Istio mTLS 为例)

- **目标:** 验证服务网格策略（例如双向 TLS, mTLS）是否被正确执行。
- **解决的问题:** 检测因 `PeerAuthentication` 策略配置错误或 sidecar 注入失败而导致 mTLS 未被强制执行的场景。
- **优化策略:**
    1. **黑盒策略测试:** 测试关注于策略的最终*效果*，而非其实现细节。
    2. **测试用例:**
        a. 在一个特定的测试命名空间中，配置一个 `PeerAuthentication` 策略为 `STRICT` (严格) 模式。
        b. Controller 指示一个**未注入** Istio sidecar 的 Agent，尝试连接该命名空间下一个**已注入** sidecar 的金丝雀服务。
        c. **预期结果:** 连接**必须失败**。如果连接成功，则证明 mTLS 策略未被正确执行。

### 3.3. IPv4/IPv6 双栈

- **目标:** 确保网络连接和策略在 IPv4 和 IPv6 两种协议栈上都能正常工作。
- **解决的问题:** 捕获在双栈集群中，一个 IP 协议栈功能完好而另一个却中断的问题。
- **优化策略:**
    1. **可配置的 IP 协议族:** 所有诊断任务（DNS, HTTP, TCP）都被设计为可接受一个 `ipFamily` 参数 (`v4`, `v6`, 或 `any`)。
    2. **显式控制:** `synapse-agent` 使用 Go 的 `net.Dialer` 和 `net.Resolver` 库来显式控制用于拨号和解析的 IP 协议栈，确保每个协议栈都得到独立测试。
