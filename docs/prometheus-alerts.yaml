groups:
  - name: synapse.network.health
    rules:
      # Node-level network health alerts
      - alert: SynapseNodeNetworkUnhealthy
        expr: synapse_node_network_health{status="unhealthy"} == 1
        for: 2m
        labels:
          severity: warning
          component: synapse
          alert_type: network_health
        annotations:
          summary: "Node {{ $labels.node }} network is unhealthy"
          description: "Node {{ $labels.node }} has been in unhealthy network state for more than 2 minutes. This may indicate network connectivity issues."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/network-unhealthy.md"

      # High network latency alerts
      - alert: SynapseHighNetworkLatency
        expr: synapse_node_network_latency_seconds{quantile="p99"} > 0.1
        for: 3m
        labels:
          severity: warning
          component: synapse
          alert_type: network_latency
        annotations:
          summary: "High network latency on node {{ $labels.node }}"
          description: "P99 network latency on node {{ $labels.node }} is {{ $value | humanizeDuration }}, which exceeds the 100ms threshold."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/high-latency.md"

      # Critical network latency alerts
      - alert: SynapseCriticalNetworkLatency
        expr: synapse_node_network_latency_seconds{quantile="p99"} > 0.5
        for: 1m
        labels:
          severity: critical
          component: synapse
          alert_type: network_latency
        annotations:
          summary: "Critical network latency on node {{ $labels.node }}"
          description: "P99 network latency on node {{ $labels.node }} is {{ $value | humanizeDuration }}, which is critically high."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/critical-latency.md"

      # High packet loss alerts
      - alert: SynapseHighPacketLoss
        expr: synapse_node_network_loss_rate > 0.05
        for: 2m
        labels:
          severity: warning
          component: synapse
          alert_type: packet_loss
        annotations:
          summary: "High packet loss on node {{ $labels.node }}"
          description: "Packet loss rate on node {{ $labels.node }} is {{ $value | humanizePercentage }}, which exceeds the 5% threshold."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/high-packet-loss.md"

      # Critical packet loss alerts
      - alert: SynapseCriticalPacketLoss
        expr: synapse_node_network_loss_rate > 0.2
        for: 1m
        labels:
          severity: critical
          component: synapse
          alert_type: packet_loss
        annotations:
          summary: "Critical packet loss on node {{ $labels.node }}"
          description: "Packet loss rate on node {{ $labels.node }} is {{ $value | humanizePercentage }}, which is critically high."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/critical-packet-loss.md"

  - name: synapse.cluster.health
    rules:
      # Cluster-wide network degradation
      - alert: SynapseClusterNetworkDegraded
        expr: synapse_cluster_unhealthy_nodes_total / (synapse_cluster_healthy_nodes_total + synapse_cluster_unhealthy_nodes_total) > 0.2
        for: 5m
        labels:
          severity: critical
          component: synapse
          alert_type: cluster_health
        annotations:
          summary: "Cluster network is degraded"
          description: "{{ $value | humanizePercentage }} of cluster nodes are experiencing network issues. This may indicate a cluster-wide network problem."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/cluster-degraded.md"

      # Majority of nodes unhealthy
      - alert: SynapseClusterNetworkCritical
        expr: synapse_cluster_unhealthy_nodes_total / (synapse_cluster_healthy_nodes_total + synapse_cluster_unhealthy_nodes_total) > 0.5
        for: 2m
        labels:
          severity: critical
          component: synapse
          alert_type: cluster_health
        annotations:
          summary: "Majority of cluster nodes have network issues"
          description: "{{ $value | humanizePercentage }} of cluster nodes are experiencing network issues. This is a critical cluster-wide network failure."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/cluster-critical.md"

      # No healthy nodes
      - alert: SynapseClusterNetworkDown
        expr: synapse_cluster_healthy_nodes_total == 0 and synapse_cluster_unhealthy_nodes_total > 0
        for: 1m
        labels:
          severity: critical
          component: synapse
          alert_type: cluster_health
        annotations:
          summary: "All cluster nodes have network issues"
          description: "All monitored nodes in the cluster are experiencing network issues. This indicates a complete network failure."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/cluster-down.md"

  - name: synapse.controller.health
    rules:
      # High probe error rate
      - alert: SynapseHighProbeErrorRate
        expr: rate(synapse_controller_probe_errors_total[5m]) / rate(synapse_node_probe_requests_total[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
          component: synapse
          alert_type: controller_health
        annotations:
          summary: "High probe error rate on node {{ $labels.node }}"
          description: "Probe error rate on node {{ $labels.node }} is {{ $value | humanizePercentage }}, which may indicate controller or network issues."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/high-error-rate.md"

      # Controller restart frequency
      - alert: SynapseControllerFrequentRestarts
        expr: increase(synapse_controller_restarts_total[1h]) > 3
        for: 0m
        labels:
          severity: warning
          component: synapse
          alert_type: controller_health
        annotations:
          summary: "Synapse controller restarting frequently"
          description: "Synapse controller has restarted {{ $value }} times in the last hour, which may indicate stability issues."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/frequent-restarts.md"

      # No probe activity
      - alert: SynapseNoProbeActivity
        expr: rate(synapse_node_probe_requests_total[10m]) == 0
        for: 5m
        labels:
          severity: critical
          component: synapse
          alert_type: controller_health
        annotations:
          summary: "No probe activity detected"
          description: "No probe requests have been made in the last 10 minutes. The Synapse controller may not be functioning properly."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/no-probe-activity.md"

  - name: synapse.sla.monitoring
    rules:
      # SLA monitoring - 99.9% availability target
      - alert: SynapseNetworkSLABreach
        expr: |
          (
            avg_over_time(synapse_cluster_healthy_nodes_total[24h]) / 
            avg_over_time((synapse_cluster_healthy_nodes_total + synapse_cluster_unhealthy_nodes_total)[24h])
          ) < 0.999
        for: 0m
        labels:
          severity: warning
          component: synapse
          alert_type: sla_monitoring
        annotations:
          summary: "Network SLA breach detected"
          description: "Network availability over the last 24 hours is {{ $value | humanizePercentage }}, which is below the 99.9% SLA target."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/sla-breach.md"

      # Weekly SLA report
      - alert: SynapseWeeklyNetworkReport
        expr: |
          (
            avg_over_time(synapse_cluster_healthy_nodes_total[7d]) / 
            avg_over_time((synapse_cluster_healthy_nodes_total + synapse_cluster_unhealthy_nodes_total)[7d])
          )
        for: 0m
        labels:
          severity: info
          component: synapse
          alert_type: sla_monitoring
        annotations:
          summary: "Weekly network availability report"
          description: "Network availability over the last 7 days is {{ $value | humanizePercentage }}."
          runbook_url: "https://github.com/your-org/synapse/blob/main/docs/runbooks/weekly-report.md"
