# Synapse Controller Prometheus 指标设计文档

## 概述

本文档描述了 Synapse Controller 的 Prometheus 指标设计，用于监控网络健康状态、延迟统计和集群整体状况。

## 核心指标设计

### 1. 节点网络健康状态指标

#### `synapse_node_network_health`

- **类型**: Gauge (0/1)
- **描述**: 节点网络健康状态，1 表示健康，0 表示异常
- **标签**:
  - `node`: 节点名称
  - `status`: 健康状态 (`healthy`/`unhealthy`)

```prometheus
synapse_node_network_health{node="node-1", status="healthy"} 1
synapse_node_network_health{node="node-2", status="unhealthy"} 1
```

### 2. 网络延迟指标

#### `synapse_node_network_latency_seconds`

- **类型**: Gauge
- **描述**: 节点网络延迟统计（秒）
- **标签**:
  - `node`: 节点名称
  - `quantile`: 延迟分位数 (`avg`/`p95`/`p99`)

```prometheus
synapse_node_network_latency_seconds{node="node-1", quantile="avg"} 0.025
synapse_node_network_latency_seconds{node="node-1", quantile="p95"} 0.045
synapse_node_network_latency_seconds{node="node-1", quantile="p99"} 0.055
```

### 3. 丢包率指标

#### `synapse_node_network_loss_rate`

- **类型**: Gauge (0.0-1.0)
- **描述**: 节点网络丢包率，范围 0.0-1.0
- **标签**:
  - `node`: 节点名称

```prometheus
synapse_node_network_loss_rate{node="node-1"} 0.02
```

### 4. 探测统计指标

#### `synapse_node_probe_requests_total`

- **类型**: Counter
- **描述**: 探测请求总数
- **标签**:
  - `node`: 节点名称
  - `status`: 请求状态 (`success`/`failed`)

#### `synapse_node_probe_duration_seconds`

- **类型**: Gauge
- **描述**: 最近一次探测持续时间（秒）
- **标签**:
  - `node`: 节点名称

#### `synapse_node_probe_last_time_seconds`

- **类型**: Gauge
- **描述**: 最近一次探测时间戳（Unix 时间戳）
- **标签**:
  - `node`: 节点名称

### 5. Controller 运行状态指标

#### `synapse_controller_active_probes`

- **类型**: Gauge
- **描述**: 当前活跃的探测任务数量
- **标签**:
  - `node`: 节点名称

#### `synapse_controller_probe_errors_total`

- **类型**: Counter
- **描述**: 探测错误总数
- **标签**:
  - `node`: 节点名称
  - `error_type`: 错误类型 (`timeout`/`connection`/`other`)

#### `synapse_controller_restarts_total`

- **类型**: Counter
- **描述**: Controller 重启次数

### 6. 集群级别聚合指标

#### `synapse_cluster_healthy_nodes_total`

- **类型**: Gauge
- **描述**: 集群中健康节点总数

#### `synapse_cluster_unhealthy_nodes_total`

- **类型**: Gauge
- **描述**: 集群中异常节点总数

#### `synapse_cluster_network_latency_seconds`

- **类型**: Gauge
- **描述**: 集群整体网络延迟统计（秒）
- **标签**:
  - `quantile`: 延迟分位数 (`avg`/`p95`/`p99`)

## 监控和告警建议

### 关键告警规则

#### 节点网络异常告警

```yaml
- alert: NodeNetworkUnhealthy
  expr: synapse_node_network_health{status="unhealthy"} == 1
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "Node {{ $labels.node }} network is unhealthy"
    description: "Node {{ $labels.node }} has been in unhealthy state for more than 2 minutes"
```

#### 集群网络异常比例告警

```yaml
- alert: ClusterNetworkDegraded
  expr: synapse_cluster_unhealthy_nodes_total / (synapse_cluster_healthy_nodes_total + synapse_cluster_unhealthy_nodes_total) > 0.2
  for: 5m
  labels:
    severity: critical
  annotations:
    summary: "More than 20% of nodes have network issues"
    description: "{{ $value | humanizePercentage }} of cluster nodes are experiencing network issues"
```

#### 网络延迟异常告警

```yaml
- alert: HighNetworkLatency
  expr: synapse_node_network_latency_seconds{quantile="p99"} > 0.1
  for: 3m
  labels:
    severity: warning
  annotations:
    summary: "High network latency on node {{ $labels.node }}"
    description: "P99 latency on node {{ $labels.node }} is {{ $value }}s"
```

#### 高丢包率告警

```yaml
- alert: HighPacketLoss
  expr: synapse_node_network_loss_rate > 0.05
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "High packet loss on node {{ $labels.node }}"
    description: "Packet loss rate on node {{ $labels.node }} is {{ $value | humanizePercentage }}"
```

### Dashboard 面板建议

#### 集群概览面板

- 健康节点数量 vs 异常节点数量（饼图）
- 集群整体延迟趋势（时间序列图）
- 集群网络健康状态热力图
- Top 10 高延迟节点（表格）

#### 节点详情面板

- 每个节点的健康状态时间线
- 节点延迟分布（P50/P95/P99）
- 节点丢包率趋势
- 探测成功率统计

#### 探测统计面板

- 探测请求总数和成功率
- 探测错误分布（按错误类型）
- 活跃探测任务数量
- Controller 重启历史

#### Controller 状态面板

- Controller 运行时间
- 内存和 CPU 使用情况
- 探测任务队列长度
- 错误率趋势

## 指标标签规范

### 标签命名规范

- 使用小写字母和下划线
- 避免使用高基数标签（如 IP 地址、时间戳）
- 标签值应该是有限且可预测的

### 常用标签

- `node`: Kubernetes 节点名称
- `status`: 状态值（healthy/unhealthy, success/failed）
- `quantile`: 分位数（avg/p95/p99）
- `error_type`: 错误类型（timeout/connection/other）

## 性能考虑

### 指标数量估算

- 假设集群有 N 个节点
- 节点级别指标：约 8N 个时间序列
- 集群级别指标：约 10 个时间序列
- 总计：约 8N + 10 个时间序列

### 内存使用

- 每个时间序列约占用 1-3KB 内存
- 100 节点集群约需要 2.4MB 内存用于指标存储

### 更新频率

- 节点状态指标：每次探测完成时更新（约 3 分钟一次）
- 集群聚合指标：每分钟计算一次
- 实时状态指标：实时更新

## 配置选项

### 指标收集配置

```yaml
metrics:
  enabled: true
  port: 8080
  path: /metrics
  updateInterval: 60s
  enableClusterAggregation: true
```

### 指标保留策略

- 建议 Prometheus 保留期：30 天
- 高精度数据保留：7 天
- 聚合数据保留：90 天
