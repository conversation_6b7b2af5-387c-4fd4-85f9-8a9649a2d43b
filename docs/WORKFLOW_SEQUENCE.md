# Synapse Workflow Sequence Diagram

This document contains a Mermaid sequence diagram that details the step-by-step workflow of the Synapse prober, focusing on the scenario of a new node being added to the cluster.

```mermaid
sequenceDiagram
    participant Admin
    participant K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Kubernetes API Server
    participant <PERSON><PERSON><PERSON><PERSON><PERSON> as DaemonSet Controller
    participant <PERSON>yna<PERSON><PERSON><PERSON><PERSON><PERSON> as synapse-controller
    participant <PERSON><PERSON><PERSON> as synapse-agent Po<PERSON> (on New Node)
    participant <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Kubernetes Scheduler

    Admin->>K8sAPIServer: 1. kubectl apply -f synapse-manifests.yaml
    note right of K8sAPIServer: Synapse components (DaemonSet, Deployment, RBAC) are created.

    DSController->>K8sAPIServer: 2. Watches for new nodes
    SynapseController->>K8sAPIServer: 3. Watches for new synapse-agent Pods

    note over K8sAPIServer, AgentPod: A new node joins the cluster

    DSController->>K8sAPIServer: 4. Creates synapse-agent Pod on the new node
    K8sAPIServer-->>SynapseController: 5. Notifies: New agent Pod created

    loop Wait for Pod to be Ready
        SynapseController->>K8sAPIServer: 6. Checks Pod status
        K8sAPIServer-->>SynapseController: Pod is Running, IP is available
    end

    loop 3-Minute Probe Cycle
        SynapseController->>AgentPod: 7. HTTP GET /ping
        AgentPod-->>SynapseController: 8. HTTP 200 OK
    end

    SynapseController->>SynapseController: 9. Analyzes results (loss rate, latency)
    alt Probe Successful
        SynapseController->>K8sAPIServer: 10. PATCH Node with annotation<br/>'synapse.io/network-status: healthy'
    else Probe Failed
        SynapseController->>K8sAPIServer: 10. PATCH Node with annotation<br/>'synapse.io/network-status: unhealthy'
    end

    note over K8sAPIServer, KubeScheduler: Later, a new business Pod needs scheduling
    KubeScheduler->>K8sAPIServer: 11. Queries for nodes with 'healthy' annotation
    K8sAPIServer-->>KubeScheduler: Returns list of healthy nodes
    KubeScheduler->>AgentPod: 12. Schedules business Pod on the healthy node

```
