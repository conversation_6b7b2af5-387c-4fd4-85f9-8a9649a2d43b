# Synapse Agent Enhancement Design

## Overview

This document outlines the comprehensive enhancement design for the `synapse-agent` component to address current limitations and improve production readiness.

## Current Issues Analysis

### 1. Missing Kubernetes Deployment Manifests
- Design documents mention `agent-daemonset.yaml` but it doesn't exist
- No Service, ConfigMap, or other supporting configurations
- Cannot actually deploy and test the agent component

### 2. Oversimplified Agent Functionality
- Current implementation only has a basic HTTP handler
- Missing health check endpoints
- No graceful shutdown mechanism
- Lacks basic observability features

### 3. Production Environment Readiness
- No Dockerfile available
- Missing resource limit configurations
- No security context settings
- Lacks monitoring and logging capabilities

### 4. Incomplete Configuration Management
- Only supports port configuration
- Missing other runtime configuration options
- No configuration validation

## Enhanced Agent Design

### 1. Core Functionality Enhancement

#### 1.1 Agent Structure
```go
type Agent struct {
    // HTTP server configuration
    Port         int
    ReadTimeout  time.Duration
    WriteTimeout time.Duration
    
    // Health checks
    HealthPath   string
    ReadyPath    string
    
    // Observability
    MetricsPath  string
    EnableMetrics bool
    
    // Graceful shutdown
    ShutdownTimeout time.Duration
}
```

#### 1.2 Endpoint Design
- **`/`**: Main probe endpoint (existing functionality)
- **`/health`**: Liveness probe
- **`/ready`**: Readiness probe
- **`/metrics`**: Prometheus metrics (optional)
- **`/version`**: Version information

#### 1.3 Metrics Collection
```go
// Agent self-monitoring metrics
var (
    requestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "synapse_agent_requests_total",
            Help: "Total number of requests received by agent",
        },
        []string{"method", "path", "status"},
    )
    
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "synapse_agent_request_duration_seconds", 
            Help: "Request duration in seconds",
        },
        []string{"method", "path"},
    )
)
```

### 2. Kubernetes Deployment Design

#### 2.1 DaemonSet Configuration
```yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: synapse-agent
  namespace: synapse-monitoring
spec:
  selector:
    matchLabels:
      app: synapse-agent
  template:
    metadata:
      labels:
        app: synapse-agent
    spec:
      containers:
      - name: agent
        image: synapse-agent:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081  
          name: metrics
        env:
        - name: LISTEN_PORT
          value: "8080"
        - name: METRICS_PORT
          value: "8081"
        resources:
          requests:
            cpu: "10m"
            memory: "16Mi"
          limits:
            cpu: "50m" 
            memory: "32Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
```

#### 2.2 Service Configuration
```yaml
apiVersion: v1
kind: Service
metadata:
  name: synapse-agent-metrics
  namespace: synapse-monitoring
spec:
  selector:
    app: synapse-agent
  ports:
  - name: metrics
    port: 8081
    targetPort: 8081
  clusterIP: None  # Headless service for metrics scraping
```

### 3. Containerization Design

#### 3.1 Multi-stage Dockerfile
```dockerfile
# Build stage
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY cmd/agent/ ./cmd/agent/
COPY internal/agent/ ./internal/agent/
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o agent ./cmd/agent

# Final stage  
FROM gcr.io/distroless/static-debian11:nonroot
COPY --from=builder /app/agent /agent
EXPOSE 8080 8081
USER nonroot:nonroot
ENTRYPOINT ["/agent"]
```

### 4. Security and Reliability Design

#### 4.1 Security Context
```yaml
securityContext:
  runAsNonRoot: true
  runAsUser: 65532
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
```

#### 4.2 Network Policy
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: synapse-agent-netpol
spec:
  podSelector:
    matchLabels:
      app: synapse-agent
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: synapse-controller
    ports:
    - protocol: TCP
      port: 8080
```

### 5. Observability Design

#### 5.1 Structured Logging
```go
type Logger struct {
    logger *slog.Logger
}

func (l *Logger) LogRequest(method, path string, duration time.Duration, status int) {
    l.logger.Info("request_handled",
        slog.String("method", method),
        slog.String("path", path), 
        slog.Duration("duration", duration),
        slog.Int("status", status),
    )
}
```

#### 5.2 Health Check Logic
```go
func (a *Agent) healthHandler(w http.ResponseWriter, r *http.Request) {
    // Check server status
    if a.isHealthy() {
        w.WriteHeader(http.StatusOK)
        w.Write([]byte("OK"))
    } else {
        w.WriteHeader(http.StatusServiceUnavailable)
        w.Write([]byte("Service Unavailable"))
    }
}
```

### 6. Configuration Management Design

#### 6.1 Configuration Structure
```go
type Config struct {
    // Server configuration
    Port            int           `env:"LISTEN_PORT" default:"8080"`
    MetricsPort     int           `env:"METRICS_PORT" default:"8081"`
    ReadTimeout     time.Duration `env:"READ_TIMEOUT" default:"5s"`
    WriteTimeout    time.Duration `env:"WRITE_TIMEOUT" default:"10s"`
    ShutdownTimeout time.Duration `env:"SHUTDOWN_TIMEOUT" default:"30s"`
    
    // Feature flags
    EnableMetrics   bool `env:"ENABLE_METRICS" default:"true"`
    EnablePprof     bool `env:"ENABLE_PPROF" default:"false"`
    
    // Logging
    LogLevel        string `env:"LOG_LEVEL" default:"info"`
    LogFormat       string `env:"LOG_FORMAT" default:"json"`
}
```

#### 6.2 ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: synapse-agent-config
  namespace: synapse-monitoring
data:
  ENABLE_METRICS: "true"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  READ_TIMEOUT: "5s"
  WRITE_TIMEOUT: "10s"
```

## Key Benefits

### 1. Production Readiness
- Complete Kubernetes deployment manifests
- Proper resource management and security contexts
- Health checks and graceful shutdown

### 2. Enhanced Observability
- Prometheus metrics for agent monitoring
- Structured logging with configurable levels
- Health and readiness endpoints

### 3. Improved Security
- Non-root container execution
- Network policies for traffic control
- Minimal attack surface with distroless images

### 4. Better Configuration Management
- Environment-based configuration
- ConfigMap integration
- Validation and defaults

### 5. Operational Excellence
- Graceful shutdown handling
- Resource optimization
- Monitoring and alerting capabilities

This enhanced design transforms the simple agent into a production-ready component that aligns with Kubernetes best practices and operational requirements.
