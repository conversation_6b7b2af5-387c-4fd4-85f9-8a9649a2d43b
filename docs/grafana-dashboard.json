{"dashboard": {"id": null, "title": "Synapse Network Health Dashboard", "tags": ["synapse", "network", "kubernetes"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "Cluster Network Health Overview", "type": "stat", "targets": [{"expr": "synapse_cluster_healthy_nodes_total", "legendFormat": "Healthy Nodes"}, {"expr": "synapse_cluster_unhealthy_nodes_total", "legendFormat": "Unhealthy Nodes"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Network Health Status by Node", "type": "table", "targets": [{"expr": "synapse_node_network_health{status=\"healthy\"}", "legendFormat": "{{node}}"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"node": "Node", "Value": "Status"}}}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Network Latency by Node (P99)", "type": "timeseries", "targets": [{"expr": "synapse_node_network_latency_seconds{quantile=\"p99\"}", "legendFormat": "{{node}} P99"}], "fieldConfig": {"defaults": {"unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Packet Loss Rate by Node", "type": "timeseries", "targets": [{"expr": "synapse_node_network_loss_rate", "legendFormat": "{{node}}"}], "fieldConfig": {"defaults": {"unit": "percentunit", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Active Probes by Node", "type": "timeseries", "targets": [{"expr": "synapse_controller_active_probes", "legendFormat": "{{node}}"}], "fieldConfig": {"defaults": {"unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Probe Success Rate", "type": "timeseries", "targets": [{"expr": "rate(synapse_node_probe_requests_total{status=\"success\"}[5m]) / rate(synapse_node_probe_requests_total[5m])", "legendFormat": "{{node}}"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 7, "title": "Probe Errors by Type", "type": "timeseries", "targets": [{"expr": "rate(synapse_controller_probe_errors_total[5m])", "legendFormat": "{{node}} - {{error_type}}"}], "fieldConfig": {"defaults": {"unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}