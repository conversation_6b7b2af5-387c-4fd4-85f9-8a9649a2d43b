# Synapse Network Prober: Design Document

## 1. Overview

### 1.1. Project Goal

Synapse is an automated tool for verifying network connectivity and quality between Pods in a Kubernetes cluster. Its primary goal is to continuously monitor the network health of newly added nodes. By performing intensive ping tests, it determines if a node's network quality (low latency, zero packet loss) meets the requirements for business-critical applications. The results are stored as annotations on the corresponding Kubernetes node objects, providing a reliable basis for scheduling decisions.

### 1.2. Philosophy

The project follows a cloud-native, Unix-like philosophy:

- **Do One Thing and Do It Well:** The agent's sole purpose is to be a network endpoint. The controller's sole purpose is to orchestrate probes and update node status.
- **Simplicity & Minimalism:** Components are designed to be as lightweight and simple as possible, inspired by tools like `traefik/whoami`.
- **Kubernetes-Native:** The tool leverages Kubernetes APIs and patterns (Controllers, `client-go`, RBAC, annotations) as its foundation.

---

## 2. Architecture

The system employs a **Controller-Agent** architecture.

- **Agent (`synapse-agent`):** A lightweight network endpoint deployed as a `DaemonSet`. It runs on every (or a subset of) worker nodes, acting as a target for network probes.

- **Controller (`synapse-controller`):** A centralized component deployed as a `Deployment`. It is the brain of the system, responsible for:
    1. **Discovering** new `synapse-agent` Pods.
    2. **Executing** network probe tests against them.
    3. **Analyzing** the results (packet loss, latency).
    4. **Updating** the parent node's annotations with the health status.

![Architecture Diagram](https://i.imgur.com/example.png)  <!-- Placeholder for a diagram -->

---

## 3. Component Design

### 3.1. Agent (`synapse-agent`)

- **Technology:** Go
- **Deployment:** `DaemonSet`
- **Functionality:**
  - Starts a minimal HTTP server on a configurable port (e.g., `8080`).
  - Provides a single root endpoint (`/`).
  - Upon receiving any HTTP request, it **immediately** responds with an `HTTP 200 OK` status and an empty body.
  - It performs no logging by default and does not parse headers or the request body, ensuring the lowest possible response latency.
- **Container Image:**
  - Built using a multi-stage Dockerfile.
  - The final image is based on `scratch` or `gcr.io/distroless/static-debian11`, containing only the statically compiled Go binary.
  - Target image size is under 10MB.
- **Resource Profile:**
  - CPU/Memory requests and limits will be kept extremely low.
  - Example: `requests: { cpu: "10m", memory: "16Mi" }`, `limits: { cpu: "50m", memory: "32Mi" }`.

### 3.2. Controller (`synapse-controller`)

- **Technology:** Go + `k8s.io/client-go`
- **Deployment:** `Deployment`
- **Core Logic:**
    1. **Initialization:** Connects to the Kubernetes API server using an in-cluster configuration and a dedicated `ServiceAccount`.
    2. **Service Discovery (Pod Watcher):** An `Informer` is used to watch for Pods matching the label `app=synapse-agent`.
        - **OnAdd/OnUpdate:** When a new Pod enters the `Running` state and has an assigned IP, it is added to a work queue for probing.
        - **OnDelete:** When a Pod is deleted, the event is logged. The status of the corresponding node might be marked as `Unknown` or simply left as is, pending a re-probe policy.
    3. **Probing Engine:**
        - A dedicated Goroutine is spawned for each probe target.
        - The engine sends rapid HTTP GET requests (e.g., every 100ms) to the agent's Pod IP for a fixed duration (e.g., 3 minutes).
        - It records the success/failure and round-trip time (RTT) for each request.
    4. **Result Analysis:**
        - After the probe duration, statistics are calculated:
            - **Packet Loss Rate:** `(failed_requests / total_requests) * 100`
            - **Latency:** Average, P95, and P99 percentiles.
        - The results are compared against configurable thresholds (e.g., `loss_rate == 0`, `p99_latency < 50ms`).
    5. **Node Annotation:**
        - The controller uses a `Patch` operation to update the annotations on the agent's parent node object.
        - **Example Annotations:**
            - `synapse.io/network-status`: `healthy` or `unhealthy`
            - `synapse.io/last-probe-time`: `2025-07-03T10:00:00Z`
            - `synapse.io/avg-latency`: `12ms`
            - `synapse.io/loss-rate`: `0.00%`

---

## 4. Kubernetes Resources

The project will be defined by a set of YAML manifests:

1. `namespace.yaml`: Creates the `synapse-monitoring` namespace.
2. `rbac.yaml`:
    - `ServiceAccount` for `synapse-controller`.
    - `ClusterRole` with permissions to `get/list/watch` Pods and `get/patch/update` Nodes.
    - `ClusterRoleBinding` to link the `ServiceAccount` to the `ClusterRole`.
3. `configmap.yaml`: Stores runtime configuration for the controller (probe duration, frequency, latency thresholds, etc.).
4. `agent-daemonset.yaml`: Defines the `DaemonSet` for `synapse-agent`.
5. `controller-deployment.yaml`: Defines the `Deployment` for `synapse-controller`.
6. `network-policy.yaml` (Optional but Recommended): A sample `NetworkPolicy` to ensure the controller can communicate with agents in secure environments.

---

## 5. Workflow

1. **Deployment:** An administrator applies the Kubernetes manifests to the cluster.
2. **Agent Rollout:** The `DaemonSet` creates a `synapse-agent` Pod on each eligible node.
3. **Controller Startup:** The `Deployment` creates the `synapse-controller` Pod.
4. **Discovery:** The controller's watcher detects the running agent Pods.
5. **New Node Scenario:**
    a. A new worker node joins the cluster and gets the appropriate labels.
    b. The `DaemonSet` controller creates a new `synapse-agent` Pod on this node.
    c. The `synapse-controller` watcher detects the new Pod.
    d. Once the Pod is `Running`, the controller initiates the 3-minute network probe.
6. **Marking Status:**
    a. The probe completes, and results are analyzed.
    b. The controller patches the new node's annotations with the network status (`healthy`/`unhealthy`).
7. **Scheduling:** Business applications can now use a `nodeAffinity` rule to ensure they are only scheduled on nodes marked as `synapse.io/network-status: healthy`.

---

## 6. Risk Analysis & Future Work

This section outlines potential challenges and areas for future improvement.

- **Single Point of Failure (SPOF):**
  - **Risk:** The controller is a single point of failure. If it crashes mid-probe, the state is lost.
  - **Mitigation:** Implement a high-availability (HA) setup with multiple replicas and a **leader election** mechanism.

- **NetworkPolicy Enforcement:**
  - **Risk:** Cluster `NetworkPolicy` rules could block traffic from the controller to agents, causing all probes to fail.
  - **Mitigation:** Provide clear documentation and a sample `NetworkPolicy` manifest.

- **API Server Load:**
  - **Risk:** Frequent node patching could strain the API server, especially in large clusters.
  - **Mitigation:** Use `Patch` instead of `Update`, batch multiple annotation changes into a single API call, and implement exponential backoff for failed API requests.

- **Probe Storms:**
  - **Risk:** A controller restart could trigger a "storm" of probes against all existing nodes simultaneously.
  - **Mitigation:** Implement a rate-limited work queue to stagger the start of probe tasks.

- **Continuous Verification:**
  - **Risk:** The current design is a one-time check. A node's network health could degrade over time.
  - **Future Work:** Add a feature for periodic re-probing (e.g., every 24 hours) to ensure nodes remain healthy.

- **Observability:**
  - **Risk:** Lack of insight into why a probe failed or why a node wasn't processed.
  - **Future Work:**
    - **Structured Logging:** Implement structured (JSON) logging for key events.
    - **Prometheus Metrics:** Expose metrics via a `/metrics` endpoint (e.g., `synapse_probes_total`, `synapse_probe_latency_seconds`).
