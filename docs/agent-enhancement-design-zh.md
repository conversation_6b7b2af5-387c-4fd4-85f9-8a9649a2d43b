# Synapse Agent 增强设计方案

## 概述

本文档详细阐述了 `synapse-agent` 组件的全面增强设计方案，旨在解决当前存在的限制并提升生产环境就绪性。

## 当前问题分析

### 1. 缺少 Kubernetes 部署清单
- 设计文档提到 `agent-daemonset.yaml` 但实际不存在
- 没有 Service、ConfigMap 或其他支持配置
- 无法实际部署和测试 agent 组件

### 2. Agent 功能过于简单
- 当前实现只有基础的 HTTP 处理器
- 缺少健康检查端点
- 没有优雅关闭机制
- 缺少基本的可观测性功能

### 3. 生产环境准备不足
- 没有可用的 Dockerfile
- 缺少资源限制配置
- 没有安全上下文设置
- 缺少监控和日志功能

### 4. 配置管理不完善
- 只支持端口配置
- 缺少其他运行时配置选项
- 没有配置验证

## 增强的 Agent 设计

### 1. 核心功能增强

#### 1.1 Agent 结构
```go
type Agent struct {
    // HTTP 服务器配置
    Port         int
    ReadTimeout  time.Duration
    WriteTimeout time.Duration
    
    // 健康检查
    HealthPath   string
    ReadyPath    string
    
    // 可观测性
    MetricsPath  string
    EnableMetrics bool
    
    // 优雅关闭
    ShutdownTimeout time.Duration
}
```

#### 1.2 端点设计
- **`/`**: 主要探测端点（现有功能）
- **`/health`**: 存活性探针
- **`/ready`**: 就绪性探针
- **`/metrics`**: Prometheus 指标（可选）
- **`/version`**: 版本信息

#### 1.3 指标收集
```go
// Agent 自监控指标
var (
    requestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "synapse_agent_requests_total",
            Help: "Agent 接收的请求总数",
        },
        []string{"method", "path", "status"},
    )
    
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "synapse_agent_request_duration_seconds", 
            Help: "请求处理时间（秒）",
        },
        []string{"method", "path"},
    )
)
```

### 2. Kubernetes 部署设计

#### 2.1 DaemonSet 配置
```yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: synapse-agent
  namespace: synapse-monitoring
spec:
  selector:
    matchLabels:
      app: synapse-agent
  template:
    metadata:
      labels:
        app: synapse-agent
    spec:
      containers:
      - name: agent
        image: synapse-agent:latest
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8081  
          name: metrics
        env:
        - name: LISTEN_PORT
          value: "8080"
        - name: METRICS_PORT
          value: "8081"
        resources:
          requests:
            cpu: "10m"
            memory: "16Mi"
          limits:
            cpu: "50m" 
            memory: "32Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
```

#### 2.2 Service 配置
```yaml
apiVersion: v1
kind: Service
metadata:
  name: synapse-agent-metrics
  namespace: synapse-monitoring
spec:
  selector:
    app: synapse-agent
  ports:
  - name: metrics
    port: 8081
    targetPort: 8081
  clusterIP: None  # 用于指标抓取的无头服务
```

### 3. 容器化设计

#### 3.1 多阶段 Dockerfile
```dockerfile
# 构建阶段
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY cmd/agent/ ./cmd/agent/
COPY internal/agent/ ./internal/agent/
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o agent ./cmd/agent

# 最终阶段
FROM gcr.io/distroless/static-debian11:nonroot
COPY --from=builder /app/agent /agent
EXPOSE 8080 8081
USER nonroot:nonroot
ENTRYPOINT ["/agent"]
```

### 4. 安全和可靠性设计

#### 4.1 安全上下文
```yaml
securityContext:
  runAsNonRoot: true
  runAsUser: 65532
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
```

#### 4.2 网络策略
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: synapse-agent-netpol
spec:
  podSelector:
    matchLabels:
      app: synapse-agent
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: synapse-controller
    ports:
    - protocol: TCP
      port: 8080
```

### 5. 可观测性设计

#### 5.1 结构化日志
```go
type Logger struct {
    logger *slog.Logger
}

func (l *Logger) LogRequest(method, path string, duration time.Duration, status int) {
    l.logger.Info("request_handled",
        slog.String("method", method),
        slog.String("path", path), 
        slog.Duration("duration", duration),
        slog.Int("status", status),
    )
}
```

#### 5.2 健康检查逻辑
```go
func (a *Agent) healthHandler(w http.ResponseWriter, r *http.Request) {
    // 检查服务器状态
    if a.isHealthy() {
        w.WriteHeader(http.StatusOK)
        w.Write([]byte("OK"))
    } else {
        w.WriteHeader(http.StatusServiceUnavailable)
        w.Write([]byte("Service Unavailable"))
    }
}
```

### 6. 配置管理设计

#### 6.1 配置结构
```go
type Config struct {
    // 服务器配置
    Port            int           `env:"LISTEN_PORT" default:"8080"`
    MetricsPort     int           `env:"METRICS_PORT" default:"8081"`
    ReadTimeout     time.Duration `env:"READ_TIMEOUT" default:"5s"`
    WriteTimeout    time.Duration `env:"WRITE_TIMEOUT" default:"10s"`
    ShutdownTimeout time.Duration `env:"SHUTDOWN_TIMEOUT" default:"30s"`
    
    // 功能开关
    EnableMetrics   bool `env:"ENABLE_METRICS" default:"true"`
    EnablePprof     bool `env:"ENABLE_PPROF" default:"false"`
    
    // 日志配置
    LogLevel        string `env:"LOG_LEVEL" default:"info"`
    LogFormat       string `env:"LOG_FORMAT" default:"json"`
}
```

#### 6.2 ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: synapse-agent-config
  namespace: synapse-monitoring
data:
  ENABLE_METRICS: "true"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  READ_TIMEOUT: "5s"
  WRITE_TIMEOUT: "10s"
```

## 主要优势

### 1. 生产环境就绪
- 完整的 Kubernetes 部署清单
- 适当的资源管理和安全上下文
- 健康检查和优雅关闭

### 2. 增强的可观测性
- 用于 agent 监控的 Prometheus 指标
- 可配置级别的结构化日志
- 健康和就绪端点

### 3. 改进的安全性
- 非 root 容器执行
- 流量控制的网络策略
- 使用 distroless 镜像的最小攻击面

### 4. 更好的配置管理
- 基于环境变量的配置
- ConfigMap 集成
- 验证和默认值

### 5. 运维卓越性
- 优雅关闭处理
- 资源优化
- 监控和告警能力

这个增强设计将简单的 agent 转变为符合 Kubernetes 最佳实践和运维要求的生产就绪组件。
