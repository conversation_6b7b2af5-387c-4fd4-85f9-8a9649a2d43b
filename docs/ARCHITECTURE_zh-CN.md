# Synapse 架构图

本文档包含一个 Mermaid 图表，用于说明 Synapse 网络探测器的系统架构。

```mermaid
graph TD
    subgraph "Kubernetes 集群"
        K8sAPIServer["Kubernetes API Server"]
        Controller["synapse-controller (Deployment)"]

        subgraph "工作节点 1"
            Agent1["synapse-agent Pod"]
        end

        subgraph "新加入的工作节点 N"
            AgentN["synapse-agent Pod"]
        end
    end

    Admin[管理员/CI-CD] -- "kubectl apply" --> K8sAPIServer

    K8sAPIServer -- "DaemonSet 确保 Agent Pod 在节点上运行" --> Agent1
    K8sAPIServer -- " " --> AgentN

    Controller -- "1. 监听 (LIST/WATCH) 新的 Agent Pod" --> K8sAPIServer
    
    subgraph "探测周期"
        direction LR
        Controller -- "2. 开始探测 (HTTP Ping)" --> AgentN
        AgentN -- "3. 响应 (HTTP Pong)" --> Controller
    end

    Controller -- "4. 分析结果并 Patch 节点注解" --> K8sAPIServer

    subgraph "最终结果"
        K8sScheduler["Kubernetes 调度器"]
        K8sAPIServer -- "节点 'node-n' 被添加注解<br/>'synapse.io/network-status: healthy'" --> K8sScheduler
        K8sScheduler -- "可以将业务 Pod 调度到健康节点" --> AgentN
    end

```

### 图例说明

- **管理员/CI-CD:** 部署 Synapse 应用清单的用户或自动化系统。
- **Kubernetes API Server:** 集群的中央控制平面。
- **synapse-controller:** 负责编排网络探测的核心逻辑。
- **synapse-agent Pod:** 运行在每个节点上的轻量级网络目标。
- **探测周期:** Controller 向 Agent 发送 Ping 并收到 Pong 的核心交互循环。
- **最终结果:** 一次成功探测的结果，节点被添加注解，使得 Kubernetes 调度器可以将新的业务 Pod 放置在该节点上。
