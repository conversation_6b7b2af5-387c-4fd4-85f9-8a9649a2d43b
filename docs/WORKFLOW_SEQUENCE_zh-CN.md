# Synapse 工作流程时序图

本文档包含一个 Mermaid 时序图，详细展示了 Synapse 探测器在新节点加入集群场景下的逐步工作流程。

```mermaid
sequenceDiagram
    participant Ad<PERSON> as 管理员
    participant K8sAPIServer as Kubernetes API Server
    participant DS<PERSON><PERSON><PERSON>er as DaemonSet 控制器
    participant SynapseController as synapse-controller
    participant Agent<PERSON><PERSON> as synapse-agent Po<PERSON> (在新节点上)
    participant <PERSON><PERSON>Scheduler as <PERSON>bernetes 调度器

    Admin->>K8sAPIServer: 1. kubectl apply -f synapse-manifests.yaml
    note right of K8sAPIServer: Synapse 相关组件 (DaemonSet, Deployment, RBAC) 被创建。

    DSController->>K8sAPIServer: 2. 监听新节点的加入
    SynapseController->>K8sAPIServer: 3. 监听新的 synapse-agent Pod

    note over K8sAPIServer, AgentPod: 一个新节点加入集群

    DSController->>K8sAPIServer: 4. 在新节点上创建 synapse-agent Pod
    K8sAPIServer-->>SynapseController: 5. 通知: 新的 agent Pod 已创建

    loop 等待 Pod 就绪
        SynapseController->>K8sAPIServer: 6. 检查 Pod 状态
        K8sAPIServer-->>SynapseController: Pod 状态为 Running, IP 已分配
    end

    loop 为期3分钟的探测周期
        SynapseController->>AgentPod: 7. HTTP GET /ping
        AgentPod-->>SynapseController: 8. HTTP 200 OK
    end

    SynapseController->>SynapseController: 9. 分析结果 (丢包率, 延迟)
    alt 探测成功
        SynapseController->>K8sAPIServer: 10. PATCH 节点, 添加注解<br/>'synapse.io/network-status: healthy'
    else 探测失败
        SynapseController->>K8sAPIServer: 10. PATCH 节点, 添加注解<br/>'synapse.io/network-status: unhealthy'
    end

    note over K8sAPIServer, KubeScheduler: 稍后, 一个新的业务 Pod 需要被调度
    KubeScheduler->>K8sAPIServer: 11. 查询带有 'healthy' 注解的节点
    K8sAPIServer-->>KubeScheduler: 返回健康节点列表
    KubeScheduler->>AgentPod: 12. 将业务 Pod 调度到健康的节点上

```
