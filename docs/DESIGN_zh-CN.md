# Synapse 网络探测器：设计文档

## 1. 概述

### 1.1. 项目目标

Synapse 是一个用于在 Kubernetes 集群中自动验证 Pod 间网络连通性与质量的工具。其主要目标是持续监控新加入节点的网络健康状况。通过执行密集的 Ping 测试，它判断一个节点的网络质量（如低延迟、零丢包）是否满足业务关键型应用的部署要求。最终结果将作为注解（Annotation）保存在对应的 Kubernetes Node 对象上，为调度决策提供可靠依据。

### 1.2. 设计哲学

本项目遵循云原生和类 Unix 的设计哲学：

- **只做一件事并把它做好：** Agent 的唯一目标是成为一个网络端点；Controller 的唯一目标是编排探测任务并更新节点状态。
- **简洁与极简主义：** 组件被设计得尽可能轻量和简单，其灵感来源于 `traefik/whoami` 等工具。
- **Kubernetes 原生：** 本工具完全基于 Kubernetes 的原生 API 和设计模式（控制器、`client-go`、RBAC、注解）构建。

---

## 2. 系统架构

系统采用 **Controller-Agent**（控制器-代理）架构。

- **Agent (`synapse-agent`):** 一个轻量级的网络端点，通过 `DaemonSet` 进行部署。它运行在每一个（或指定的）工作节点上，作为网络探测的目标。

- **Controller (`synapse-controller`):** 一个中心化的组件，通过 `Deployment` 进行部署。它是整个系统的大脑，负责：
    1. **发现** 新的 `synapse-agent` Pod。
    2. **执行** 针对这些 Pod 的网络探测任务。
    3. **分析** 探测结果（丢包率、延迟）。
    4. **更新** 其所在节点的注解以标记网络健康状态。

![架构图](https://i.imgur.com/example.png) <!-- 架构图占位符 -->

---

## 3. 组件设计

### 3.1. Agent (`synapse-agent`)

- **技术栈:** Go
- **部署方式:** `DaemonSet`
- **核心功能:**
  - 启动一个极简的 HTTP 服务器，监听在可配置的端口上（例如 `8080`）。
  - 提供一个根路由 (`/`)。
  - 在收到任何 HTTP 请求时，**立即** 返回 `HTTP 200 OK` 状态码和一个空响应体。
  - 默认不记录日志，也不解析请求头或请求体，以确保最低的响应延迟。
- **容器镜像:**
  - 使用多阶段 Dockerfile 进行构建。
  - 最终镜像基于 `scratch` 或 `gcr.io/distroless/static-debian11`，仅包含静态编译的 Go 二进制文件。
  - 目标镜像大小应小于 10MB。
- **资源规格:**
  - CPU 和内存的 `requests` 与 `limits` 将被设置得极低。
  - 示例: `requests: { cpu: "10m", memory: "16Mi" }`, `limits: { cpu: "50m", memory: "32Mi" }`。

### 3.2. Controller (`synapse-controller`)

- **技术栈:** Go + `k8s.io/client-go`
- **部署方式:** `Deployment`
- **核心逻辑:**
    1. **初始化:** 使用集群内配置（In-Cluster Config）和一个专用的 `ServiceAccount` 连接到 Kubernetes API Server。
    2. **服务发现 (Pod Watcher):** 使用 `Informer` 机制来监视匹配标签 `app=synapse-agent` 的 Pod。
        - **OnAdd/OnUpdate:** 当一个新 Pod 进入 `Running` 状态并被分配了 IP 地址后，它被加入到一个工作队列中等待探测。
        - **OnDelete:** 当一个 Pod 被删除时，记录该事件。对应节点的网络状态可能会被标记为 `Unknown` 或根据重新探测策略保持不变。
    3. **探测引擎:**
        - 为每个探测目标启动一个独立的 Goroutine。
        - 该引擎在固定周期内（例如 3 分钟），以高频率（例如每 100 毫秒）向 Agent 的 Pod IP 发送 HTTP GET 请求。
        - 记录每次请求的成功/失败以及往返时延（RTT）。
    4. **结果分析:**
        - 探测周期结束后，计算统计数据：
            - **丢包率:** `(失败请求数 / 总请求数) * 100`
            - **延迟:** 平均值、P95 和 P99 百分位延迟。
        - 将结果与可配置的阈值进行比较（例如 `丢包率 == 0`, `P99 延迟 < 50ms`）。
    5. **节点注解:**
        - Controller 使用 `Patch` 操作来更新 Agent 所在节点对象的注解。
        - **注解示例:**
            - `synapse.io/network-status`: `healthy` 或 `unhealthy`
            - `synapse.io/last-probe-time`: `2025-07-03T10:00:00Z`
            - `synapse.io/avg-latency`: `12ms`
            - `synapse.io/loss-rate`: `0.00%`

---

## 4. Kubernetes 资源清单

项目将由一套标准的 YAML 清单文件定义：

1. `namespace.yaml`: 创建 `synapse-monitoring` 命名空间。
2. `rbac.yaml`:
    - 为 `synapse-controller` 创建 `ServiceAccount`。
    - 创建 `ClusterRole`，授予 `get/list/watch` Pods 和 `get/patch/update` Nodes 的权限。
    - 创建 `ClusterRoleBinding`，将 `ServiceAccount` 与 `ClusterRole` 绑定。
3. `configmap.yaml`: 存储 Controller 的运行时配置（探测时长、频率、延迟阈值等）。
4. `agent-daemonset.yaml`: 定义 `synapse-agent` 的 `DaemonSet`。
5. `controller-deployment.yaml`: 定义 `synapse-controller` 的 `Deployment`。
6. `network-policy.yaml` (可选但推荐): 提供一个 `NetworkPolicy` 示例，以确保 Controller 在安全环境中能与 Agent 通信。

---

## 5. 工作流程

1. **部署:** 管理员在集群中应用（apply）所有 Kubernetes 清单文件。
2. **Agent 分发:** `DaemonSet` 在所有符合条件的节点上创建 `synapse-agent` Pod。
3. **Controller 启动:** `Deployment` 创建 `synapse-controller` Pod。
4. **服务发现:** Controller 的 Watcher 检测到正在运行的 Agent Pods。
5. **新节点加入场景:**
    a. 一个新的工作节点加入集群，并被打上正确的标签。
    b. `DaemonSet` 控制器立即在该新节点上创建一个 `synapse-agent` Pod。
    c. `synapse-controller` 的 Watcher 检测到这个新 Pod。
    d. 一旦 Pod 进入 `Running` 状态，Controller 就对其发起为期 3 分钟的网络探测。
6. **标记状态:**
    a. 探测完成，分析结果。
    b. Controller 通过 Patch 操作更新新节点的注解，标记其网络状态 (`healthy`/`unhealthy`)。
7. **应用调度:** 业务应用现在可以使用 `nodeAffinity` 规则，确保它们只被调度到被标记为 `synapse.io/network-status: healthy` 的节点上。

---

## 6. 风险分析与未来工作

本节概述了潜在的挑战和未来可改进的方向。

- **单点故障 (SPOF):**
  - **风险:** Controller 是一个单点故障。如果它在探测过程中崩溃，探测状态会丢失。
  - **缓解措施:** 实现高可用（HA）部署，即运行多个 Controller 副本并引入**领导者选举**机制。

- **网络策略 (NetworkPolicy) 限制:**
  - **风险:** 集群中严格的 `NetworkPolicy` 规则可能会阻止 Controller 与 Agent 之间的通信，导致所有探测失败。
  - **缓解措施:** 提供清晰的文档说明和一个 `NetworkPolicy` 的示例清单文件。

- **API Server 负载:**
  - **风险:** 在大规模集群中，频繁地更新节点对象可能会对 API Server 造成压力。
  - **缓解措施:** 使用 `Patch` 代替 `Update` 操作，将多个注解的修改合并为单次 API 调用，并为失败的 API 请求实现指数退避重试机制。

- **探测风暴 (Probing Storms):**
  - **风险:** Controller 重启可能会同时对所有存量节点发起探测，瞬间产生大量网络流量。
  - **缓解措施:** 实现一个带速率限制的工作队列，以平滑探测任务的启动。

- **持续验证:**
  - **风险:** 当前设计是一次性检查。节点的网络健康状况可能随时间推移而劣化。
  - **未来工作:** 增加周期性重新探测的功能（例如每 24 小时一次），以确保持续的健康状态。

- **可观测性 (Observability):**
  - **风险:** 当系统行为不符合预期时，缺乏有效的排查手段。
  - **未来工作:**
    - **结构化日志:** 为关键事件实现结构化（JSON 格式）日志。
    - **Prometheus 指标:** 通过 `/metrics` 端点暴露核心指标（例如 `synapse_probes_total`, `synapse_probe_latency_seconds`）。
