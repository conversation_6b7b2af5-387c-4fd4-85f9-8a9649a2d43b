# Advanced Network Diagnostics Platform

This document outlines the design for a comprehensive network diagnostics platform built upon the Synapse Controller-Agent architecture. It moves beyond simple connectivity checks to diagnose complex issues across the entire Kubernetes networking stack, from the CNI to the Ingress. The strategies described here prioritize reliability, low intrusiveness, and a focus on "black-box" testing of outcomes over "white-box" inspection of implementation details.

---

## 1. Service Abstraction Layer Diagnostics

This suite of tests focuses on the Kubernetes Service abstraction, ensuring that traffic routing managed by `kube-proxy` and DNS resolution managed by `CoreDNS` are functioning correctly.

### 1.1. ClusterIP Reachability

- **Goal:** Verify that a Service is reachable via its stable `ClusterIP` from any Pod in the cluster.
- **Problem Solved:** Detects broken or stale `kube-proxy` rules (iptables/IPVS) that prevent traffic from reaching a Service's backend.
- **Refined Strategy:**
    1. **Target Selection:** The Controller targets a stable, pre-existing Service (e.g., `kube-dns` in the `kube-system` namespace) or a dedicated, long-running "canary" application deployed by Synapse. This avoids the overhead and risk of dynamically creating resources for each test.
    2. **Execution:** The Controller instructs a random `synapse-agent` to perform an HTTP GET request (or a TCP connection test) to the target's `http://<ClusterIP>:<Port>`.
    3. **Validation:** The test passes if the agent receives a successful response (e.g., HTTP 200 OK).

### 1.2. NodePort Reachability

- **Goal:** Verify that a Service is reachable via its `NodePort` from other nodes in the cluster.
- **Problem Solved:** Identifies misconfigured host firewalls, cloud security groups, or network ACLs that block cross-node traffic to a specific `NodePort`.
- **Refined Strategy:**
    1. **Target Selection:** Uses a dedicated canary Service of type `NodePort`.
    2. **Execution:** The Controller instructs an agent on Node A to connect to the `NodePort` on Node B (`http://<NodeB_IP>:<NodePort>`).
    3. **Validation:** The test passes on a successful connection, confirming the cross-node data path is open.

### 1.3. Endpoints Synchronization

- **Goal:** Ensure a Service's `Endpoints` object is correctly and promptly synchronized with its set of healthy backend Pods.
- **Problem Solved:** Detects situations where traffic is being sent to non-existent or terminating Pods ("black-holing") due to delays or failures in the endpoint controller.
- **Refined Strategy:**
    1. **API-Based Comparison:** The Controller directly compares two lists fetched from the Kubernetes API:
        - List A: The IP addresses from the `Endpoints` object.
        - List B: The IP addresses of all Pods in the `Running` state that match the Service's label selector.
    2. **Resilience:** To avoid false positives from normal scheduling latency, the check ignores Pods in the `Terminating` state and implements a retry mechanism. An alert is only triggered if the lists remain inconsistent over a defined time window (e.g., 15 seconds).

### 1.4. Headless Service DNS Resolution

- **Goal:** Verify that a Headless Service's DNS name correctly resolves to the IP addresses of all its backend Pods.
- **Problem Solved:** Catches DNS caching issues or `CoreDNS` misconfigurations that prevent stateful applications from discovering their peers.
- **Refined Strategy:**
    1. **Cache Busting:** The `synapse-agent` performs a DNS lookup for a canary Headless Service. To bypass potential caching, the agent can either query the `kube-dns` service IP directly or append a random prefix to the hostname for each query.
    2. **Validation:** The Controller compares the list of IPs returned by the DNS query with the actual list of backend Pod IPs obtained from the Kubernetes API.

---

## 2. North-South Traffic Diagnostics

These tests validate the flow of traffic into (Ingress) and out of (Egress) the cluster.

### 2.1. Ingress Path Verification

- **Goal:** Validate the traffic path from the Ingress controller to the backend Pod.
- **Problem Solved:** Pinpoints misconfigured `Ingress` rules, TLS termination issues, or broken links between the Ingress, the Service, and the Pods.
- **Refined Strategy:**
  - **Internal Test (Default):** An agent *inside* the cluster sends an HTTP request to the Ingress controller's internal IP, providing the correct `Host` header. This effectively tests the `Ingress Controller -> Service -> Pod` portion of the path.
  - **External Test (Optional Extension):** For true end-to-end validation, an optional, external prober can be deployed. This component would test the entire path, including public DNS resolution, external load balancers, and cloud firewalls.

### 2.2. Egress Connectivity & NAT Gateway

- **Goal:** Verify that Pods can access external services and identify their public source IP.
- **Problem Solved:** Detects restrictive egress firewall rules, failing NAT gateways, or public IP blocklisting.
- **Refined Strategy:**
    1. **Redundancy:** The `synapse-agent` attempts to connect to a list of multiple, redundant public "echo IP" services (e.g., `https://api.ipify.org`, `https://ifconfig.me`).
    2. **Quorum:** The test is considered successful if a quorum (e.g., 2 out of 3) of the external services respond successfully. This prevents false alarms caused by a single external service's failure.
    3. **Configuration:** The list of external endpoints is user-configurable, allowing them to specify their own highly-available targets.

---

## 3. Low-Level Infrastructure & Advanced Features

These diagnostics inspect the health of the underlying CNI and advanced networking layers like Service Mesh.

### 3.1. CNI-Specific Health (Example: Calico)

- **Goal:** Check the health of CNI-specific components that are invisible to standard connectivity tests.
- **Problem Solved:** Identifies failures in underlying CNI mechanisms, such as broken BGP peering in Calico.
- **Refined Strategy:**
    1. **API-First Approach:** Instead of parsing the output of a CLI tool like `calicoctl`, the Controller interacts directly with the CNI's Custom Resource Definitions (CRDs).
    2. **Execution:** For Calico, the Controller would list `BGPPeer` resources via the Kubernetes API and check their `.status` field to ensure all peering sessions are in the `Established` state. This method is robust and immune to changes in CLI output formatting.

### 3.2. Service Mesh Health (Example: Istio mTLS)

- **Goal:** Verify that Service Mesh policies, such as mutual TLS (mTLS), are being correctly enforced.
- **Problem Solved:** Detects scenarios where mTLS is not being enforced due to misconfigured `PeerAuthentication` policies or sidecar injection failures.
- **Refined Strategy:**
    1. **Black-Box Policy Testing:** The test focuses on the *outcome* of the policy, not its implementation details.
    2. **Test Case:**
        a. A `PeerAuthentication` policy is configured to `STRICT` for a specific test namespace.
        b. The Controller instructs an agent **without** an Istio sidecar to attempt to connect to a canary service **with** an Istio sidecar within that namespace.
        c. **Expected Result:** The connection **must fail**. If it succeeds, it proves that the mTLS policy is not being correctly enforced.

### 3.3. IPv4/IPv6 Dual-Stack

- **Goal:** Ensure network connectivity and policies function correctly over both IPv4 and IPv6 protocol families.
- **Problem Solved:** Catches issues where one IP stack is fully functional while the other is broken in a dual-stack cluster.
- **Refined Strategy:**
    1. **Configurable IP Family:** All diagnostic tasks (DNS, HTTP, TCP) are designed to accept an `ipFamily` parameter (`v4`, `v6`, or `any`).
    2. **Explicit Control:** The `synapse-agent` uses Go's `net.Dialer` and `net.Resolver` libraries to explicitly control which IP stack is used for dialing and resolution, ensuring each stack is tested independently.
