# Synapse Controller Prometheus Metrics Design Document

## Overview

This document describes the Prometheus metrics design for Synapse Controller, which monitors network health status, latency statistics, and overall cluster conditions.

## Core Metrics Design

### 1. Node Network Health Status Metrics

#### `synapse_node_network_health`

- **Type**: Gauge (0/1)
- **Description**: Node network health status, 1 for healthy, 0 for unhealthy
- **Labels**:
  - `node`: Node name
  - `status`: Health status (`healthy`/`unhealthy`)

```prometheus
synapse_node_network_health{node="node-1", status="healthy"} 1
synapse_node_network_health{node="node-2", status="unhealthy"} 1
```

### 2. Network Latency Metrics

#### `synapse_node_network_latency_seconds`

- **Type**: Gauge
- **Description**: Node network latency statistics (seconds)
- **Labels**:
  - `node`: Node name
  - `quantile`: Latency quantile (`avg`/`p95`/`p99`)

```prometheus
synapse_node_network_latency_seconds{node="node-1", quantile="avg"} 0.025
synapse_node_network_latency_seconds{node="node-1", quantile="p95"} 0.045
synapse_node_network_latency_seconds{node="node-1", quantile="p99"} 0.055
```

### 3. Packet Loss Rate Metrics

#### `synapse_node_network_loss_rate`

- **Type**: Gauge (0.0-1.0)
- **Description**: Node network packet loss rate, range 0.0-1.0
- **Labels**:
  - `node`: Node name

```prometheus
synapse_node_network_loss_rate{node="node-1"} 0.02
```

### 4. Probe Statistics Metrics

#### `synapse_node_probe_requests_total`

- **Type**: Counter
- **Description**: Total number of probe requests
- **Labels**:
  - `node`: Node name
  - `status`: Request status (`success`/`failed`)

#### `synapse_node_probe_duration_seconds`

- **Type**: Gauge
- **Description**: Duration of the last probe (seconds)
- **Labels**:
  - `node`: Node name

#### `synapse_node_probe_last_time_seconds`

- **Type**: Gauge
- **Description**: Timestamp of the last probe (Unix timestamp)
- **Labels**:
  - `node`: Node name

### 5. Controller Runtime Status Metrics

#### `synapse_controller_active_probes`

- **Type**: Gauge
- **Description**: Number of currently active probe tasks
- **Labels**:
  - `node`: Node name

#### `synapse_controller_probe_errors_total`

- **Type**: Counter
- **Description**: Total number of probe errors
- **Labels**:
  - `node`: Node name
  - `error_type`: Error type (`timeout`/`connection`/`other`)

#### `synapse_controller_restarts_total`

- **Type**: Counter
- **Description**: Number of controller restarts

### 6. Cluster-Level Aggregated Metrics

#### `synapse_cluster_healthy_nodes_total`

- **Type**: Gauge
- **Description**: Total number of healthy nodes in the cluster

#### `synapse_cluster_unhealthy_nodes_total`

- **Type**: Gauge
- **Description**: Total number of unhealthy nodes in the cluster

#### `synapse_cluster_network_latency_seconds`

- **Type**: Gauge
- **Description**: Cluster-wide network latency statistics (seconds)
- **Labels**:
  - `quantile`: Latency quantile (`avg`/`p95`/`p99`)

## Monitoring and Alerting Recommendations

### Key Alerting Rules

#### Node Network Unhealthy Alert

```yaml
- alert: NodeNetworkUnhealthy
  expr: synapse_node_network_health{status="unhealthy"} == 1
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "Node {{ $labels.node }} network is unhealthy"
    description: "Node {{ $labels.node }} has been in unhealthy state for more than 2 minutes"
```

#### Cluster Network Degraded Alert

```yaml
- alert: ClusterNetworkDegraded
  expr: synapse_cluster_unhealthy_nodes_total / (synapse_cluster_healthy_nodes_total + synapse_cluster_unhealthy_nodes_total) > 0.2
  for: 5m
  labels:
    severity: critical
  annotations:
    summary: "More than 20% of nodes have network issues"
    description: "{{ $value | humanizePercentage }} of cluster nodes are experiencing network issues"
```

#### High Network Latency Alert

```yaml
- alert: HighNetworkLatency
  expr: synapse_node_network_latency_seconds{quantile="p99"} > 0.1
  for: 3m
  labels:
    severity: warning
  annotations:
    summary: "High network latency on node {{ $labels.node }}"
    description: "P99 latency on node {{ $labels.node }} is {{ $value }}s"
```

#### High Packet Loss Alert

```yaml
- alert: HighPacketLoss
  expr: synapse_node_network_loss_rate > 0.05
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "High packet loss on node {{ $labels.node }}"
    description: "Packet loss rate on node {{ $labels.node }} is {{ $value | humanizePercentage }}"
```

### Dashboard Panel Recommendations

#### Cluster Overview Panel

- Healthy vs Unhealthy nodes count (pie chart)
- Cluster-wide latency trends (time series)
- Network health status heatmap
- Top 10 high-latency nodes (table)

#### Node Details Panel

- Health status timeline for each node
- Node latency distribution (P50/P95/P99)
- Node packet loss trends
- Probe success rate statistics

#### Probe Statistics Panel

- Total probe requests and success rate
- Probe error distribution (by error type)
- Active probe task count
- Controller restart history

#### Controller Status Panel

- Controller uptime
- Memory and CPU usage
- Probe task queue length
- Error rate trends

## Metrics Label Standards

### Label Naming Conventions

- Use lowercase letters and underscores
- Avoid high-cardinality labels (like IP addresses, timestamps)
- Label values should be finite and predictable

### Common Labels

- `node`: Kubernetes node name
- `status`: Status value (healthy/unhealthy, success/failed)
- `quantile`: Quantile (avg/p95/p99)
- `error_type`: Error type (timeout/connection/other)

## Performance Considerations

### Metrics Count Estimation

- Assuming a cluster with N nodes
- Node-level metrics: approximately 8N time series
- Cluster-level metrics: approximately 10 time series
- Total: approximately 8N + 10 time series

### Memory Usage

- Each time series uses approximately 1-3KB memory
- A 100-node cluster requires about 2.4MB memory for metrics storage

### Update Frequency

- Node status metrics: Updated after each probe completion (~every 3 minutes)
- Cluster aggregated metrics: Calculated every minute
- Real-time status metrics: Updated in real-time

## Configuration Options

### Metrics Collection Configuration

```yaml
metrics:
  enabled: true
  port: 8080
  path: /metrics
  updateInterval: 60s
  enableClusterAggregation: true
```

### Metrics Retention Policy

- Recommended Prometheus retention: 30 days
- High-precision data retention: 7 days
- Aggregated data retention: 90 days
