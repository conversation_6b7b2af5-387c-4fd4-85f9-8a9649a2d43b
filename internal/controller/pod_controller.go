/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
)

// PodReconciler reconciles synapse-agent pods
type PodReconciler struct {
	client.Client
	Scheme      *runtime.Scheme
	ProbeEngine *ProbeEngine
	NodeUpdater *NodeUpdater
	Config      *ProbeConfig
}

// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch
// +kubebuilder:rbac:groups="",resources=nodes,verbs=get;patch;update

// Reconcile handles Pod events for synapse-agent pods
func (r *PodReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx).WithValues("pod", req.NamespacedName)

	// Get the pod
	pod := &corev1.Pod{}
	if err := r.Get(ctx, req.NamespacedName, pod); err != nil {
		if client.IgnoreNotFound(err) == nil {
			// Pod was deleted, stop any active probe
			logger.Info("Pod deleted, stopping probe if active")
			r.ProbeEngine.StopProbe(req.Name)
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Failed to get pod")
		return ctrl.Result{}, err
	}

	// Double check if this is a synapse-agent pod
	if !r.isSynapseAgentPod(pod) {
		logger.Info("Pod is not a synapse-agent pod")
		return ctrl.Result{}, nil
	}

	logger = logger.WithValues("podIP", pod.Status.PodIP, "node", pod.Spec.NodeName)

	// Check if pod is ready for probing
	if !r.isPodReadyForProbing(pod) {
		logger.Info("Pod not ready for probing", "phase", pod.Status.Phase, "podIP", pod.Status.PodIP)
		return ctrl.Result{}, nil
	}

	// Start probe if not already running
	resultChan, err := r.ProbeEngine.StartProbe(ctx, pod.Name, pod.Status.PodIP, pod.Spec.NodeName)
	if err != nil {
		logger.Info("Probe already running or failed to start", "error", err)
		return ctrl.Result{}, nil
	}

	// Start a goroutine to handle the probe result
	go r.handleProbeResult(ctx, resultChan)

	logger.Info("Started network probe for pod")
	return ctrl.Result{}, nil
}

// isSynapseAgentPod checks if the pod is a synapse-agent pod
func (r *PodReconciler) isSynapseAgentPod(pod *corev1.Pod) bool {
	if pod.Labels == nil {
		return false
	}
	return pod.Labels["app"] == "synapse-agent"
}

// isPodReadyForProbing checks if the pod is ready for network probing
func (r *PodReconciler) isPodReadyForProbing(pod *corev1.Pod) bool {
	// Pod must be in Running phase
	if pod.Status.Phase != corev1.PodRunning {
		return false
	}

	// Pod must have an IP address
	if pod.Status.PodIP == "" {
		return false
	}

	// Pod must be scheduled to a node
	if pod.Spec.NodeName == "" {
		return false
	}

	// Check if all containers are ready
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
			return true
		}
	}

	return false
}

// handleProbeResult handles the result of a network probe
func (r *PodReconciler) handleProbeResult(ctx context.Context, resultChan <-chan *ProbeResult) {
	logger := log.FromContext(ctx)

	select {
	case result := <-resultChan:
		if result == nil {
			logger.Info("Received nil probe result")
			return
		}

		logger = logger.WithValues("pod", result.PodName, "node", result.NodeName)
		logger.Info(
			"Received probe result",
			"totalRequests", result.TotalRequests,
			"successfulRequests", result.SuccessfulRequests,
			"failedRequests", result.FailedRequests,
			"lossRate", fmt.Sprintf("%.2f%%", result.LossRate()),
			"avgLatency", result.AvgLatency(),
			"p99Latency", result.P99Latency(),
		)

		// Update node annotations with the probe result
		if err := r.NodeUpdater.UpdateNodeWithProbeResult(ctx, result); err != nil {
			logger.Error(err, "Failed to update node annotations")
		}

	case <-ctx.Done():
		logger.Info("Context cancelled while waiting for probe result")
	}
}

// SetupWithManager sets up the controller with the Manager
func (r *PodReconciler) SetupWithManager(mgr ctrl.Manager) error {
	filter := predicate.NewPredicateFuncs(
		func(object client.Object) bool {
			pod, ok := object.(*corev1.Pod)
			if !ok {
				return false
			}
			// Only watch synapse-agent pods
			return r.isSynapseAgentPod(pod)
		},
	)

	return ctrl.NewControllerManagedBy(mgr).
		For(&corev1.Pod{}).
		WithEventFilter(filter).
		Named("synapse-pod").
		Complete(r)
}
