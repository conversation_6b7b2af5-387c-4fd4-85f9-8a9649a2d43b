/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
)

// PodReconciler reconciles synapse-agent pods
type PodReconciler struct {
	client.Client
	Scheme      *runtime.Scheme
	ProbeEngine *ProbeEngine
	NodeUpdater *NodeUpdater
	Config      *ProbeConfig
	startTime   time.Time // Track when the controller started
}

// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch
// +kubebuilder:rbac:groups="",resources=nodes,verbs=get;patch;update

// Reconcile handles Pod events for synapse-agent pods
func (r *PodReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx).WithValues("pod", req.NamespacedName)

	// Get the pod
	pod := &corev1.Pod{}
	if err := r.Get(ctx, req.NamespacedName, pod); err != nil {
		if client.IgnoreNotFound(err) == nil {
			// Pod was deleted, stop any active probe
			logger.Info("Pod deleted, stopping probe if active")
			r.ProbeEngine.StopProbe(req.Name)
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Failed to get pod")
		return ctrl.Result{}, err
	}

	// Double check if this is a synapse-agent pod
	if !r.isSynapseAgentPod(pod) {
		logger.Info("Pod is not a synapse-agent pod")
		return ctrl.Result{}, nil
	}

	logger = logger.WithValues("podIP", pod.Status.PodIP, "node", pod.Spec.NodeName)

	// Check if pod is ready for probing
	if !r.isPodReadyForProbing(pod) {
		logger.Info("Pod not ready for probing", "phase", pod.Status.Phase, "podIP", pod.Status.PodIP)
		return ctrl.Result{}, nil
	}

	// Check if node has already been probed recently to avoid re-probing on controller restart
	if r.shouldSkipProbe(ctx, pod.Spec.NodeName) {
		logger.Info("Node already probed recently, skipping probe")
		return ctrl.Result{}, nil
	}

	// Add a small random delay to spread out probe starts on controller restart
	// This prevents all probes from starting simultaneously
	delay := r.calculateProbeDelay(pod.Spec.NodeName)
	if delay > 0 {
		logger.Info("Adding delay before starting probe to spread load", "delay", delay)
		return ctrl.Result{RequeueAfter: delay}, nil
	}

	// Start probe if not already running
	resultChan, err := r.ProbeEngine.StartProbe(ctx, pod.Name, pod.Status.PodIP, pod.Spec.NodeName)
	if err != nil {
		logger.Info("Probe already running or failed to start", "error", err)
		return ctrl.Result{}, nil
	}

	// Start a goroutine to handle the probe result
	go r.handleProbeResult(ctx, resultChan)

	logger.Info("Started network probe for pod")
	return ctrl.Result{}, nil
}

// isSynapseAgentPod checks if the pod is a synapse-agent pod
func (r *PodReconciler) isSynapseAgentPod(pod *corev1.Pod) bool {
	if pod.Labels == nil {
		return false
	}
	return pod.Labels["app"] == "synapse-agent"
}

// isPodReadyForProbing checks if the pod is ready for network probing
func (r *PodReconciler) isPodReadyForProbing(pod *corev1.Pod) bool {
	// Pod must be in Running phase
	if pod.Status.Phase != corev1.PodRunning {
		return false
	}

	// Pod must have an IP address
	if pod.Status.PodIP == "" {
		return false
	}

	// Pod must be scheduled to a node
	if pod.Spec.NodeName == "" {
		return false
	}

	// Check if all containers are ready
	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
			return true
		}
	}

	return false
}

// calculateProbeDelay calculates a delay to spread out probe starts on controller restart
func (r *PodReconciler) calculateProbeDelay(nodeName string) time.Duration {
	// Use a hash of the node name to get a consistent but distributed delay
	// This ensures the same node always gets the same delay, but different nodes get different delays
	hash := 0
	for _, c := range nodeName {
		hash = hash*31 + int(c)
	}

	// Convert to a positive number and get a delay between 0 and MaxStartupDelay
	maxDelayMs := int(r.Config.MaxStartupDelay.Milliseconds())
	if maxDelayMs <= 0 {
		return 0
	}

	delay := time.Duration(hash%maxDelayMs) * time.Millisecond
	if delay < 0 {
		delay = -delay
	}

	return delay
}

// SetStartTime sets the controller start time for handling restarts
func (r *PodReconciler) SetStartTime(t time.Time) {
	r.startTime = t
}

// shouldSkipProbe checks if we should skip probing a node to avoid duplicate probes on controller restart
func (r *PodReconciler) shouldSkipProbe(ctx context.Context, nodeName string) bool {
	logger := log.FromContext(ctx).WithValues("node", nodeName)

	// Get current network status from node annotations
	status, err := r.NodeUpdater.GetNodeNetworkStatus(ctx, nodeName)
	if err != nil {
		logger.V(1).Info("Failed to get node network status, will proceed with probe", "error", err)
		return false
	}

	// If no status exists, we should probe
	if status == "" {
		return false
	}

	// Get the last probe time
	lastProbeTime, err := r.NodeUpdater.GetLastProbeTime(ctx, nodeName)
	if err != nil {
		logger.V(1).Info("Failed to get last probe time, will proceed with probe", "error", err)
		return false
	}

	// If we can't parse the time, proceed with probe
	if lastProbeTime.IsZero() {
		return false
	}

	// Special handling for controller restart:
	// If the last probe was completed before the controller started, and it was recent,
	// we should skip to avoid immediate re-probing
	if !r.startTime.IsZero() && lastProbeTime.Before(r.startTime) {
		// Check if the probe was completed recently (within configured grace period)
		if time.Since(lastProbeTime) < r.Config.RestartGracePeriod {
			logger.Info(
				"Node was probed recently before controller restart, skipping",
				"lastProbeTime", lastProbeTime,
				"controllerStartTime", r.startTime,
				"timeSince", time.Since(lastProbeTime),
				"gracePeriod", r.Config.RestartGracePeriod)
			return true
		}
	}

	// Normal case: skip if probed very recently
	recentThreshold := 30 * time.Second
	if time.Since(lastProbeTime) < recentThreshold {
		logger.Info(
			"Node was probed very recently, skipping",
			"lastProbeTime", lastProbeTime,
			"timeSince", time.Since(lastProbeTime),
			"threshold", recentThreshold)
		return true
	}

	return false
}

// handleProbeResult handles the result of a network probe
func (r *PodReconciler) handleProbeResult(ctx context.Context, resultChan <-chan *ProbeResult) {
	logger := log.FromContext(ctx)

	select {
	case result := <-resultChan:
		if result == nil {
			logger.Info("Received nil probe result")
			return
		}

		logger = logger.WithValues("pod", result.PodName, "node", result.NodeName)
		logger.Info(
			"Received probe result",
			"totalRequests", result.TotalRequests,
			"successfulRequests", result.SuccessfulRequests,
			"failedRequests", result.FailedRequests,
			"lossRate", fmt.Sprintf("%.2f%%", result.LossRate()),
			"avgLatency", result.AvgLatency(),
			"p99Latency", result.P99Latency(),
		)

		// Update node annotations with the probe result
		if err := r.NodeUpdater.UpdateNodeWithProbeResult(ctx, result); err != nil {
			logger.Error(err, "Failed to update node annotations")
		}

	case <-ctx.Done():
		logger.Info("Context cancelled while waiting for probe result")
	}
}

// SetupWithManager sets up the controller with the Manager
func (r *PodReconciler) SetupWithManager(mgr ctrl.Manager) error {
	filter := predicate.NewPredicateFuncs(
		func(object client.Object) bool {
			pod, ok := object.(*corev1.Pod)
			if !ok {
				return false
			}
			// Only watch synapse-agent pods
			return r.isSynapseAgentPod(pod)
		},
	)

	return ctrl.NewControllerManagedBy(mgr).
		For(&corev1.Pod{}).
		WithEventFilter(filter).
		Named("synapse-pod").
		Complete(r)
}
