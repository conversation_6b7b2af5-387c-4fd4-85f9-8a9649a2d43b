/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"sigs.k8s.io/controller-runtime/pkg/log"
)

// ProbeEngine manages network probing tasks
type ProbeEngine struct {
	config       *ProbeConfig  // Configuration for network probing
	httpClient   *http.Client  // HTTP client for probing
	activeProbes sync.Map      // map[string]*ProbeTask, key is pod name
	semaphore    chan struct{} // Semaphore to limit concurrent probes
}

// ProbeTask represents an active probing task
type ProbeTask struct {
	PodName  string             // Key for activeProbes map
	PodIP    string             // IP address of the pod
	NodeName string             // Name of the node
	Cancel   context.CancelFunc // Cancel function for the probe context
	Result   chan *ProbeResult  // Channel to send the probe result
}

// NewProbeEngine creates a new probe engine
func NewProbeEngine(config *ProbeConfig) *ProbeEngine {
	return &ProbeEngine{
		config: config,
		httpClient: &http.Client{
			Timeout: 5 * time.Second, // 5 second timeout for each request
		},
		semaphore: make(chan struct{}, config.MaxConcurrentProbes),
	}
}

// StartProbe starts a new probe task for the given pod
func (pe *ProbeEngine) StartProbe(ctx context.Context, podName, podIP, nodeName string) (<-chan *ProbeResult, error) {
	logger := log.FromContext(ctx).WithValues("pod", podName, "podIP", podIP, "node", nodeName)

	// Check if probe is already running for this pod
	if _, exists := pe.activeProbes.Load(podName); exists {
		logger.Info("Probe already running for pod, skipping")
		return nil, nil
	}

	// Create probe task
	probeCtx, cancel := context.WithCancel(ctx)
	resultChan := make(chan *ProbeResult, 1)

	task := &ProbeTask{
		PodName:  podName,
		PodIP:    podIP,
		NodeName: nodeName,
		Cancel:   cancel,
		Result:   resultChan,
	}

	// Store the active probe
	pe.activeProbes.Store(podName, task)

	// Start the probe in a goroutine
	go pe.runProbe(probeCtx, task)

	logger.Info("Started network probe", "duration", pe.config.ProbeDuration, "interval", pe.config.ProbeInterval)
	return resultChan, nil
}

// StopProbe stops an active probe task
func (pe *ProbeEngine) StopProbe(podName string) {
	if taskInterface, exists := pe.activeProbes.LoadAndDelete(podName); exists {
		task := taskInterface.(*ProbeTask)
		task.Cancel()
	}
}

// runProbe executes the actual probing logic
func (pe *ProbeEngine) runProbe(ctx context.Context, task *ProbeTask) {
	logger := log.FromContext(ctx).WithValues("pod", task.PodName, "podIP", task.PodIP)

	// Acquire semaphore to limit concurrent probes
	select {
	case pe.semaphore <- struct{}{}:
		defer func() {
			<-pe.semaphore
		}()
	case <-ctx.Done():
		logger.Info("Probe cancelled before starting")
		pe.activeProbes.Delete(task.PodName)
		return
	}

	// Initialize probe result
	result := &ProbeResult{
		PodName:   task.PodName,
		PodIP:     task.PodIP,
		NodeName:  task.NodeName,
		StartTime: time.Now(),
		Latencies: make([]time.Duration, 0),
	}

	// Calculate total number of requests based on duration and interval
	totalDuration := pe.config.ProbeDuration
	interval := pe.config.ProbeInterval

	logger.Info("Starting probe execution", "totalDuration", totalDuration, "interval", interval)

	// Create ticker for probe interval
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	// Create timeout for total probe duration
	timeout := time.After(totalDuration)

	// Target URL for the agent
	targetURL := fmt.Sprintf("http://%s:%d/", task.PodIP, pe.config.AgentPort)

	for {
		select {
		case <-ctx.Done():
			logger.Info("Probe cancelled")
			result.EndTime = time.Now()
			pe.sendResult(task, result)
			return

		case <-timeout:
			logger.Info(
				"Probe duration completed",
				"totalRequests", result.TotalRequests,
				"successful", result.SuccessfulRequests,
				"failed", result.FailedRequests,
			)
			result.EndTime = time.Now()
			pe.sendResult(task, result)
			return

		case <-ticker.C:
			pe.sendSingleProbe(ctx, targetURL, result)
		}
	}
}

// sendSingleProbe sends a single HTTP probe request
func (pe *ProbeEngine) sendSingleProbe(ctx context.Context, targetURL string, result *ProbeResult) {
	logger := log.FromContext(ctx).WithValues("pod", result.PodName, "podIP", result.PodIP)

	result.TotalRequests++

	startTime := time.Now()

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		result.FailedRequests++
		logger.Error(err, "Failed to create HTTP request")
		return
	}

	resp, err := pe.httpClient.Do(req)
	latency := time.Since(startTime)

	if err != nil {
		result.FailedRequests++
		logger.Error(err, "Failed to send HTTP request")
		return
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			logger.Error(err, "Failed to close response body")
		}
	}()

	if resp.StatusCode == http.StatusOK {
		result.SuccessfulRequests++
		result.Latencies = append(result.Latencies, latency)
	} else {
		result.FailedRequests++
	}
}

// sendResult sends the probe result and cleans up
func (pe *ProbeEngine) sendResult(task *ProbeTask, result *ProbeResult) {
	// Remove from active probes
	pe.activeProbes.Delete(task.PodName)

	// Send result
	select {
	case task.Result <- result:
	default:
		// Channel might be closed, ignore
	}
	close(task.Result)
}

// GetActiveProbeCount returns the number of currently active probes
func (pe *ProbeEngine) GetActiveProbeCount() int {
	count := 0
	pe.activeProbes.Range(func(key, value any) bool {
		count++
		return true
	})
	return count
}
