/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"
)

const (
	// Label key for node network status
	LabelNetworkStatus = "synapse.io/network-status"

	// Annotation keys for node network status
	AnnotationNetworkStatus   = "synapse.io/network-status"
	AnnotationLastProbeTime   = "synapse.io/last-probe-time"
	AnnotationAvgLatency      = "synapse.io/avg-latency"
	AnnotationP95Latency      = "synapse.io/p95-latency"
	AnnotationP99Latency      = "synapse.io/p99-latency"
	AnnotationLossRate        = "synapse.io/loss-rate"
	AnnotationTotalRequests   = "synapse.io/total-requests"
	AnnotationSuccessRequests = "synapse.io/success-requests"
	AnnotationFailedRequests  = "synapse.io/failed-requests"

	// Network status values
	NetworkStatusHealthy   = "healthy"
	NetworkStatusUnhealthy = "unhealthy"
)

// NodeUpdater handles updating node annotations with probe results
type NodeUpdater struct {
	client client.Client
	config *ProbeConfig
}

// NewNodeUpdater creates a new node updater
func NewNodeUpdater(cli client.Client, config *ProbeConfig) *NodeUpdater {
	return &NodeUpdater{
		client: cli,
		config: config,
	}
}

// UpdateNodeWithProbeResult updates the node annotations with probe results
func (nu *NodeUpdater) UpdateNodeWithProbeResult(ctx context.Context, result *ProbeResult) error {
	logger := log.FromContext(ctx).WithValues("node", result.NodeName, "pod", result.PodName)

	// Get the node
	node := &corev1.Node{}
	if err := nu.client.Get(ctx, types.NamespacedName{Name: result.NodeName}, node); err != nil {
		logger.Error(err, "Failed to get node")
		return fmt.Errorf("failed to get node %s: %w", result.NodeName, err)
	}

	// Prepare the patch
	patch := client.MergeFrom(node.DeepCopy())

	// Initialize labels if nil
	if node.Labels == nil {
		node.Labels = make(map[string]string)
	}

	// Initialize annotations if nil
	if node.Annotations == nil {
		node.Annotations = make(map[string]string)
	}

	// Determine network status
	isHealthy := result.IsHealthy(nu.config.HealthThresholds)
	networkStatus := NetworkStatusUnhealthy
	if isHealthy {
		networkStatus = NetworkStatusHealthy
	}

	// Update labels
	node.Labels[LabelNetworkStatus] = networkStatus

	// Update annotations
	node.Annotations[AnnotationNetworkStatus] = networkStatus
	node.Annotations[AnnotationLastProbeTime] = result.EndTime.Format(time.RFC3339)
	node.Annotations[AnnotationAvgLatency] = formatDuration(result.AvgLatency())
	node.Annotations[AnnotationP95Latency] = formatDuration(result.P95Latency())
	node.Annotations[AnnotationP99Latency] = formatDuration(result.P99Latency())
	node.Annotations[AnnotationLossRate] = fmt.Sprintf("%.2f%%", result.LossRate())
	node.Annotations[AnnotationTotalRequests] = fmt.Sprintf("%d", result.TotalRequests)
	node.Annotations[AnnotationSuccessRequests] = fmt.Sprintf("%d", result.SuccessfulRequests)
	node.Annotations[AnnotationFailedRequests] = fmt.Sprintf("%d", result.FailedRequests)

	// Apply the patch
	if err := nu.client.Patch(ctx, node, patch); err != nil {
		logger.Error(err, "Failed to patch node annotations")
		return fmt.Errorf("failed to patch node %s: %w", result.NodeName, err)
	}

	logger.Info("Successfully updated node annotations",
		"networkStatus", networkStatus,
		"avgLatency", result.AvgLatency(),
		"p99Latency", result.P99Latency(),
		"lossRate", fmt.Sprintf("%.2f%%", result.LossRate()),
		"totalRequests", result.TotalRequests,
		"successfulRequests", result.SuccessfulRequests,
		"failedRequests", result.FailedRequests,
	)

	return nil
}

// formatDuration formats a duration for annotation values
func formatDuration(d time.Duration) string {
	if d == 0 {
		return "0ms"
	}

	// Convert to milliseconds for readability
	ms := float64(d.Nanoseconds()) / 1e6
	if ms < 1 {
		return fmt.Sprintf("%.3fms", ms)
	} else if ms < 1000 {
		return fmt.Sprintf("%.1fms", ms)
	} else {
		return fmt.Sprintf("%.1fs", ms/1000)
	}
}

// GetNodeNetworkStatus retrieves the current network status of a node
func (nu *NodeUpdater) GetNodeNetworkStatus(ctx context.Context, nodeName string) (string, error) {
	node := &corev1.Node{}
	if err := nu.client.Get(ctx, types.NamespacedName{Name: nodeName}, node); err != nil {
		return "", fmt.Errorf("failed to get node %s: %w", nodeName, err)
	}

	if node.Annotations == nil {
		return "", nil
	}

	return node.Annotations[AnnotationNetworkStatus], nil
}

// ClearNodeAnnotations removes all synapse-related annotations from a node
func (nu *NodeUpdater) ClearNodeAnnotations(ctx context.Context, nodeName string) error {
	logger := log.FromContext(ctx).WithValues("node", nodeName)

	// Get the node
	node := &corev1.Node{}
	if err := nu.client.Get(ctx, types.NamespacedName{Name: nodeName}, node); err != nil {
		logger.Error(err, "Failed to get node")
		return fmt.Errorf("failed to get node %s: %w", nodeName, err)
	}

	// Check if there are any synapse annotations to remove
	if node.Annotations == nil {
		return nil
	}

	// Prepare the patch
	patch := client.MergeFrom(node.DeepCopy())

	// Remove synapse annotations
	annotationsToRemove := []string{
		AnnotationNetworkStatus,
		AnnotationLastProbeTime,
		AnnotationAvgLatency,
		AnnotationP95Latency,
		AnnotationP99Latency,
		AnnotationLossRate,
		AnnotationTotalRequests,
		AnnotationSuccessRequests,
		AnnotationFailedRequests,
	}

	removed := false
	for _, annotation := range annotationsToRemove {
		if _, exists := node.Annotations[annotation]; exists {
			delete(node.Annotations, annotation)
			removed = true
		}
	}

	// Apply the patch if any annotations were removed
	if removed {
		if err := nu.client.Patch(ctx, node, patch); err != nil {
			logger.Error(err, "Failed to patch node annotations")
			return fmt.Errorf("failed to patch node %s: %w", nodeName, err)
		}
		logger.Info("Successfully cleared node annotations")
	}

	return nil
}
