/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

// Register metrics with controller-runtime
func init() {
	metrics.Registry.MustRegister(
		nodeHealthGauge,
		nodeLatencyGauge,
		nodeLossRateGauge,
		probeRequestsCounter,
		probeDurationGauge,
		probeLastTimeGauge,
		activeProbesGauge,
		probeErrorsCounter,
		controllerRestartsCounter,
		clusterHealthyNodesGauge,
		clusterUnhealthyNodesGauge,
		clusterLatencyGauge,
	)
}

var (
	// Node-level metrics
	nodeHealthGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_network_health",
			Help: "Node network health status (1=healthy, 0=unhealthy)",
		},
		[]string{"node", "status"},
	)

	nodeLatencyGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_network_latency_seconds",
			Help: "Node network latency statistics in seconds",
		},
		[]string{"node", "quantile"},
	)

	nodeLossRateGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_network_loss_rate",
			Help: "Node network packet loss rate (0.0-1.0)",
		},
		[]string{"node"},
	)

	// Probe statistics metrics
	probeRequestsCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "synapse_node_probe_requests_total",
			Help: "Total number of probe requests",
		},
		[]string{"node", "status"},
	)

	probeDurationGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_probe_duration_seconds",
			Help: "Duration of the last probe in seconds",
		},
		[]string{"node"},
	)

	probeLastTimeGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_probe_last_time_seconds",
			Help: "Timestamp of the last probe (Unix timestamp)",
		},
		[]string{"node"},
	)

	// Controller status metrics
	activeProbesGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_controller_active_probes",
			Help: "Number of currently active probe tasks",
		},
		[]string{"node"},
	)

	probeErrorsCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "synapse_controller_probe_errors_total",
			Help: "Total number of probe errors",
		},
		[]string{"node", "error_type"},
	)

	controllerRestartsCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "synapse_controller_restarts_total",
			Help: "Number of controller restarts",
		},
	)

	// Cluster-level metrics
	clusterHealthyNodesGauge = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "synapse_cluster_healthy_nodes_total",
			Help: "Total number of healthy nodes in the cluster",
		},
	)

	clusterUnhealthyNodesGauge = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "synapse_cluster_unhealthy_nodes_total",
			Help: "Total number of unhealthy nodes in the cluster",
		},
	)

	clusterLatencyGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_cluster_network_latency_seconds",
			Help: "Cluster-wide network latency statistics in seconds",
		},
		[]string{"quantile"},
	)
)

// MetricsCollector is responsible for updating Prometheus metrics
type MetricsCollector struct {
	// Internal state
	mu           sync.RWMutex
	nodeStatuses map[string]bool // node -> healthy status
	enabled      bool
}

// NewMetricsCollector creates a new MetricsCollector
func NewMetricsCollector(enabled bool) *MetricsCollector {
	return &MetricsCollector{
		enabled:      enabled,
		nodeStatuses: make(map[string]bool),
	}
}

// RecordControllerRestart increments the controller restart counter
func (mc *MetricsCollector) RecordControllerRestart() {
	if !mc.enabled {
		return
	}
	controllerRestartsCounter.Inc()
}

// RecordProbeStart records the start of a probe
func (mc *MetricsCollector) RecordProbeStart(nodeName string) {
	if !mc.enabled {
		return
	}
	activeProbesGauge.WithLabelValues(nodeName).Inc()
}

// RecordProbeEnd records the end of a probe
func (mc *MetricsCollector) RecordProbeEnd(nodeName string, duration time.Duration, success bool) {
	if !mc.enabled {
		return
	}

	// Update active probes count
	activeProbesGauge.WithLabelValues(nodeName).Dec()

	// Update probe duration
	probeDurationGauge.WithLabelValues(nodeName).Set(duration.Seconds())

	// Update last probe time
	probeLastTimeGauge.WithLabelValues(nodeName).Set(float64(time.Now().Unix()))

	// Update probe requests counter
	status := "success"
	if !success {
		status = "failed"
	}
	probeRequestsCounter.WithLabelValues(nodeName, status).Inc()
}

// RecordProbeError records a probe error
func (mc *MetricsCollector) RecordProbeError(nodeName, errorType string) {
	if !mc.enabled {
		return
	}
	probeErrorsCounter.WithLabelValues(nodeName, errorType).Inc()
}

// UpdateNodeHealth updates node health status metrics
func (mc *MetricsCollector) UpdateNodeHealth(nodeName string, healthy bool) {
	if !mc.enabled {
		return
	}

	mc.mu.Lock()
	defer mc.mu.Unlock()

	// Update node status tracking
	oldStatus, existed := mc.nodeStatuses[nodeName]
	mc.nodeStatuses[nodeName] = healthy

	// Update node health gauge
	if healthy {
		nodeHealthGauge.WithLabelValues(nodeName, "healthy").Set(1)
		nodeHealthGauge.WithLabelValues(nodeName, "unhealthy").Set(0)
	} else {
		nodeHealthGauge.WithLabelValues(nodeName, "healthy").Set(0)
		nodeHealthGauge.WithLabelValues(nodeName, "unhealthy").Set(1)
	}

	// Update cluster aggregates if status changed or new node
	if !existed || oldStatus != healthy {
		mc.updateClusterAggregates()
	}
}

// UpdateNodeLatency updates node latency metrics
func (mc *MetricsCollector) UpdateNodeLatency(nodeName string, avgLatency, p95Latency, p99Latency time.Duration) {
	if !mc.enabled {
		return
	}

	nodeLatencyGauge.WithLabelValues(nodeName, "avg").Set(avgLatency.Seconds())
	nodeLatencyGauge.WithLabelValues(nodeName, "p95").Set(p95Latency.Seconds())
	nodeLatencyGauge.WithLabelValues(nodeName, "p99").Set(p99Latency.Seconds())
}

// UpdateNodeLossRate updates node packet loss rate metrics
func (mc *MetricsCollector) UpdateNodeLossRate(nodeName string, lossRate float64) {
	if !mc.enabled {
		return
	}
	nodeLossRateGauge.WithLabelValues(nodeName).Set(lossRate)
}

// updateClusterAggregates updates cluster-level aggregated metrics
func (mc *MetricsCollector) updateClusterAggregates() {
	healthyCount := 0
	unhealthyCount := 0

	for _, healthy := range mc.nodeStatuses {
		if healthy {
			healthyCount++
		} else {
			unhealthyCount++
		}
	}

	clusterHealthyNodesGauge.Set(float64(healthyCount))
	clusterUnhealthyNodesGauge.Set(float64(unhealthyCount))
}

// StartClusterAggregation starts a goroutine to periodically update cluster-level metrics
func (mc *MetricsCollector) StartClusterAggregation(ctx context.Context, interval time.Duration) {
	if !mc.enabled {
		return
	}

	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		logger := log.FromContext(ctx).WithName("metrics-aggregator")

		for {
			select {
			case <-ctx.Done():
				logger.Info("Stopping cluster metrics aggregation")
				return
			case <-ticker.C:
				mc.updateClusterLatencyAggregates()
			}
		}
	}()
}

// updateClusterLatencyAggregates calculates and updates cluster-wide latency statistics
func (mc *MetricsCollector) updateClusterLatencyAggregates() {
	// This is a simplified implementation
	// In a real implementation, you might want to collect latency data from all nodes
	// and calculate proper cluster-wide percentiles

	// For now, we'll leave this as a placeholder
	// The actual implementation would require collecting latency samples from all nodes
	// and computing cluster-wide statistics
}
