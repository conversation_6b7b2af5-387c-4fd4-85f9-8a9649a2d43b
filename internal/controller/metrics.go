/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/metrics"
)

// MetricsCollector collects and manages Prometheus metrics for Synapse Controller
type MetricsCollector struct {
	// Node-level metrics
	nodeHealthGauge     *prometheus.GaugeVec
	nodeLatencyGauge    *prometheus.GaugeVec
	nodeLossRateGauge   *prometheus.GaugeVec

	// Probe statistics metrics
	probeRequestsCounter *prometheus.CounterVec
	probeDurationGauge   *prometheus.GaugeVec
	probeLastTimeGauge   *prometheus.GaugeVec

	// Controller status metrics
	activeProbesGauge     *prometheus.GaugeVec
	probeErrorsCounter    *prometheus.CounterVec
	controllerRestartsCounter prometheus.Counter

	// Cluster-level metrics
	clusterHealthyNodesGauge   prometheus.Gauge
	clusterUnhealthyNodesGauge prometheus.Gauge
	clusterLatencyGauge        *prometheus.GaugeVec

	// Internal state
	mu           sync.RWMutex
	nodeStatuses map[string]bool // node -> healthy status
	enabled      bool
}

// NewMetricsCollector creates a new MetricsCollector
func NewMetricsCollector(enabled bool) *MetricsCollector {
	if !enabled {
		return &MetricsCollector{enabled: false}
	}

	mc := &MetricsCollector{
		enabled:      true,
		nodeStatuses: make(map[string]bool),
	}

	// Initialize node-level metrics
	mc.nodeHealthGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_network_health",
			Help: "Node network health status (1=healthy, 0=unhealthy)",
		},
		[]string{"node", "status"},
	)

	mc.nodeLatencyGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_network_latency_seconds",
			Help: "Node network latency statistics in seconds",
		},
		[]string{"node", "quantile"},
	)

	mc.nodeLossRateGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_network_loss_rate",
			Help: "Node network packet loss rate (0.0-1.0)",
		},
		[]string{"node"},
	)

	// Initialize probe statistics metrics
	mc.probeRequestsCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "synapse_node_probe_requests_total",
			Help: "Total number of probe requests",
		},
		[]string{"node", "status"},
	)

	mc.probeDurationGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_probe_duration_seconds",
			Help: "Duration of the last probe in seconds",
		},
		[]string{"node"},
	)

	mc.probeLastTimeGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_node_probe_last_time_seconds",
			Help: "Timestamp of the last probe (Unix timestamp)",
		},
		[]string{"node"},
	)

	// Initialize controller status metrics
	mc.activeProbesGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_controller_active_probes",
			Help: "Number of currently active probe tasks",
		},
		[]string{"node"},
	)

	mc.probeErrorsCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "synapse_controller_probe_errors_total",
			Help: "Total number of probe errors",
		},
		[]string{"node", "error_type"},
	)

	mc.controllerRestartsCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "synapse_controller_restarts_total",
			Help: "Number of controller restarts",
		},
	)

	// Initialize cluster-level metrics
	mc.clusterHealthyNodesGauge = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "synapse_cluster_healthy_nodes_total",
			Help: "Total number of healthy nodes in the cluster",
		},
	)

	mc.clusterUnhealthyNodesGauge = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Name: "synapse_cluster_unhealthy_nodes_total",
			Help: "Total number of unhealthy nodes in the cluster",
		},
	)

	mc.clusterLatencyGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_cluster_network_latency_seconds",
			Help: "Cluster-wide network latency statistics in seconds",
		},
		[]string{"quantile"},
	)

	// Register all metrics
	metrics.Registry.MustRegister(
		mc.nodeHealthGauge,
		mc.nodeLatencyGauge,
		mc.nodeLossRateGauge,
		mc.probeRequestsCounter,
		mc.probeDurationGauge,
		mc.probeLastTimeGauge,
		mc.activeProbesGauge,
		mc.probeErrorsCounter,
		mc.controllerRestartsCounter,
		mc.clusterHealthyNodesGauge,
		mc.clusterUnhealthyNodesGauge,
		mc.clusterLatencyGauge,
	)

	return mc
}

// RecordControllerRestart increments the controller restart counter
func (mc *MetricsCollector) RecordControllerRestart() {
	if !mc.enabled {
		return
	}
	mc.controllerRestartsCounter.Inc()
}

// RecordProbeStart records the start of a probe
func (mc *MetricsCollector) RecordProbeStart(nodeName string) {
	if !mc.enabled {
		return
	}
	mc.activeProbesGauge.WithLabelValues(nodeName).Inc()
}

// RecordProbeEnd records the end of a probe
func (mc *MetricsCollector) RecordProbeEnd(nodeName string, duration time.Duration, success bool) {
	if !mc.enabled {
		return
	}

	// Update active probes count
	mc.activeProbesGauge.WithLabelValues(nodeName).Dec()

	// Update probe duration
	mc.probeDurationGauge.WithLabelValues(nodeName).Set(duration.Seconds())

	// Update last probe time
	mc.probeLastTimeGauge.WithLabelValues(nodeName).Set(float64(time.Now().Unix()))

	// Update probe requests counter
	status := "success"
	if !success {
		status = "failed"
	}
	mc.probeRequestsCounter.WithLabelValues(nodeName, status).Inc()
}

// RecordProbeError records a probe error
func (mc *MetricsCollector) RecordProbeError(nodeName, errorType string) {
	if !mc.enabled {
		return
	}
	mc.probeErrorsCounter.WithLabelValues(nodeName, errorType).Inc()
}

// UpdateNodeHealth updates node health status metrics
func (mc *MetricsCollector) UpdateNodeHealth(nodeName string, healthy bool) {
	if !mc.enabled {
		return
	}

	mc.mu.Lock()
	defer mc.mu.Unlock()

	// Update node status tracking
	oldStatus, existed := mc.nodeStatuses[nodeName]
	mc.nodeStatuses[nodeName] = healthy

	// Update node health gauge
	if healthy {
		mc.nodeHealthGauge.WithLabelValues(nodeName, "healthy").Set(1)
		mc.nodeHealthGauge.WithLabelValues(nodeName, "unhealthy").Set(0)
	} else {
		mc.nodeHealthGauge.WithLabelValues(nodeName, "healthy").Set(0)
		mc.nodeHealthGauge.WithLabelValues(nodeName, "unhealthy").Set(1)
	}

	// Update cluster aggregates if status changed or new node
	if !existed || oldStatus != healthy {
		mc.updateClusterAggregates()
	}
}

// UpdateNodeLatency updates node latency metrics
func (mc *MetricsCollector) UpdateNodeLatency(nodeName string, avgLatency, p95Latency, p99Latency time.Duration) {
	if !mc.enabled {
		return
	}

	mc.nodeLatencyGauge.WithLabelValues(nodeName, "avg").Set(avgLatency.Seconds())
	mc.nodeLatencyGauge.WithLabelValues(nodeName, "p95").Set(p95Latency.Seconds())
	mc.nodeLatencyGauge.WithLabelValues(nodeName, "p99").Set(p99Latency.Seconds())
}

// UpdateNodeLossRate updates node packet loss rate metrics
func (mc *MetricsCollector) UpdateNodeLossRate(nodeName string, lossRate float64) {
	if !mc.enabled {
		return
	}
	mc.nodeLossRateGauge.WithLabelValues(nodeName).Set(lossRate)
}

// updateClusterAggregates updates cluster-level aggregated metrics
func (mc *MetricsCollector) updateClusterAggregates() {
	healthyCount := 0
	unhealthyCount := 0

	for _, healthy := range mc.nodeStatuses {
		if healthy {
			healthyCount++
		} else {
			unhealthyCount++
		}
	}

	mc.clusterHealthyNodesGauge.Set(float64(healthyCount))
	mc.clusterUnhealthyNodesGauge.Set(float64(unhealthyCount))
}

// StartClusterAggregation starts a goroutine to periodically update cluster-level metrics
func (mc *MetricsCollector) StartClusterAggregation(ctx context.Context, interval time.Duration) {
	if !mc.enabled {
		return
	}

	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		logger := log.FromContext(ctx).WithName("metrics-aggregator")

		for {
			select {
			case <-ctx.Done():
				logger.Info("Stopping cluster metrics aggregation")
				return
			case <-ticker.C:
				mc.updateClusterLatencyAggregates()
			}
		}
	}()
}

// updateClusterLatencyAggregates calculates and updates cluster-wide latency statistics
func (mc *MetricsCollector) updateClusterLatencyAggregates() {
	// This is a simplified implementation
	// In a real implementation, you might want to collect latency data from all nodes
	// and calculate proper cluster-wide percentiles
	
	// For now, we'll leave this as a placeholder
	// The actual implementation would require collecting latency samples from all nodes
	// and computing cluster-wide statistics
}
