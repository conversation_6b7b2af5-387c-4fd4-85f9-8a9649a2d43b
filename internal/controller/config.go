/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"sort"
	"time"
)

// ProbeConfig contains configuration for network probing
type ProbeConfig struct {
	// ProbeInterval is the interval between probe requests (default: 100ms)
	ProbeInterval time.Duration

	// ProbeDuration is the total duration of probing (default: 3 minutes)
	ProbeDuration time.Duration

	// MaxConcurrentProbes is the maximum number of concurrent probe tasks (default: 30)
	MaxConcurrentProbes int

	// AgentPort is the port that synapse-agent listens on (fixed: 8080)
	AgentPort int

	// HealthThresholds contains thresholds for determining network health
	HealthThresholds HealthThresholds

	// Controller restart handling
	// RestartGracePeriod is the grace period to avoid re-probing after restart (default: probe duration + 60s)
	RestartGracePeriod time.Duration

	// MaxStartupDelay is the maximum delay to spread probe starts on restart (default: 30s)
	MaxStartupDelay time.Duration
}

// HealthThresholds contains thresholds for network health determination
type HealthThresholds struct {
	// MaxLossRate is the maximum acceptable packet loss rate (default: 0.0)
	MaxLossRate float64

	// MaxP99Latency is the maximum acceptable P99 latency in milliseconds (default: 50ms)
	MaxP99Latency time.Duration
}

// DefaultProbeConfig returns the default probe configuration
func DefaultProbeConfig() *ProbeConfig {
	return &ProbeConfig{
		ProbeInterval:       100 * time.Millisecond,
		ProbeDuration:       3 * time.Minute,
		MaxConcurrentProbes: 30,
		AgentPort:           8080,
		HealthThresholds: HealthThresholds{
			MaxLossRate:   0.0,
			MaxP99Latency: 50 * time.Millisecond,
		},
		RestartGracePeriod: 4 * time.Minute, // 3min probe + 1min buffer
		MaxStartupDelay:    30 * time.Second,
	}
}

// ProbeResult contains the result of a network probe
type ProbeResult struct {
	// PodName is the name of the probed pod
	PodName string

	// PodIP is the IP address of the probed pod
	PodIP string

	// NodeName is the name of the node where the pod is running
	NodeName string

	// TotalRequests is the total number of requests sent
	TotalRequests int

	// SuccessfulRequests is the number of successful requests
	SuccessfulRequests int

	// FailedRequests is the number of failed requests
	FailedRequests int

	// Latencies contains all recorded latencies
	Latencies []time.Duration

	// StartTime is when the probe started
	StartTime time.Time

	// EndTime is when the probe ended
	EndTime time.Time
}

// LossRate calculates the packet loss rate as a percentage
func (pr *ProbeResult) LossRate() float64 {
	if pr.TotalRequests == 0 {
		return 0.0
	}
	return float64(pr.FailedRequests) / float64(pr.TotalRequests) * 100.0
}

// AvgLatency calculates the average latency
func (pr *ProbeResult) AvgLatency() time.Duration {
	if len(pr.Latencies) == 0 {
		return 0
	}

	var total time.Duration
	for _, latency := range pr.Latencies {
		total += latency
	}
	return total / time.Duration(len(pr.Latencies))
}

// percentileLatency returns the Nth percentile latency from a slice
func percentileLatency(latencies []time.Duration, percentile float64) time.Duration {
	if len(latencies) == 0 {
		return 0
	}
	sorted := make([]time.Duration, len(latencies))
	copy(sorted, latencies)
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i] < sorted[j]
	})
	index := int(float64(len(sorted)) * percentile)
	if index >= len(sorted) {
		index = len(sorted) - 1
	}
	return sorted[index]
}

// P99Latency calculates the 99th percentile latency
func (pr *ProbeResult) P99Latency() time.Duration {
	return percentileLatency(pr.Latencies, 0.99)
}

// P95Latency calculates the 95th percentile latency
func (pr *ProbeResult) P95Latency() time.Duration {
	return percentileLatency(pr.Latencies, 0.95)
}

// IsHealthy determines if the probe result indicates a healthy network
func (pr *ProbeResult) IsHealthy(thresholds HealthThresholds) bool {
	lossRate := pr.LossRate()
	p99Latency := pr.P99Latency()

	return lossRate <= thresholds.MaxLossRate && p99Latency <= thresholds.MaxP99Latency
}
