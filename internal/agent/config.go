/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package agent

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

// Config holds the configuration for the synapse-agent
type Config struct {
	// Server configuration
	Port            int           `env:"LISTEN_PORT"`
	MetricsPort     int           `env:"METRICS_PORT"`
	ReadTimeout     time.Duration `env:"READ_TIMEOUT"`
	WriteTimeout    time.Duration `env:"WRITE_TIMEOUT"`
	ShutdownTimeout time.Duration `env:"SHUTDOWN_TIMEOUT"`

	// Feature flags
	EnableMetrics bool `env:"ENABLE_METRICS"`
	EnablePprof   bool `env:"ENABLE_PPROF"`

	// Logging
	LogLevel  string `env:"LOG_LEVEL"`
	LogFormat string `env:"LOG_FORMAT"`

	// Health check paths
	HealthPath  string `env:"HEALTH_PATH"`
	ReadyPath   string `env:"READY_PATH"`
	MetricsPath string `env:"METRICS_PATH"`
	VersionPath string `env:"VERSION_PATH"`
}

// NewConfig creates a new Config with default values
func NewConfig() *Config {
	return &Config{
		// Server defaults
		Port:            8080,
		MetricsPort:     8081,
		ReadTimeout:     5 * time.Second,
		WriteTimeout:    10 * time.Second,
		ShutdownTimeout: 30 * time.Second,

		// Feature defaults
		EnableMetrics: true,
		EnablePprof:   false,

		// Logging defaults
		LogLevel:  "info",
		LogFormat: "json",

		// Path defaults
		HealthPath:  "/health",
		ReadyPath:   "/ready",
		MetricsPath: "/metrics",
		VersionPath: "/version",
	}
}

// LoadFromEnv loads configuration from environment variables
func (c *Config) LoadFromEnv() error {
	if port := os.Getenv("LISTEN_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			c.Port = p
		} else {
			return fmt.Errorf("invalid LISTEN_PORT: %v", err)
		}
	}

	if metricsPort := os.Getenv("METRICS_PORT"); metricsPort != "" {
		if p, err := strconv.Atoi(metricsPort); err == nil {
			c.MetricsPort = p
		} else {
			return fmt.Errorf("invalid METRICS_PORT: %v", err)
		}
	}

	if readTimeout := os.Getenv("READ_TIMEOUT"); readTimeout != "" {
		if d, err := time.ParseDuration(readTimeout); err == nil {
			c.ReadTimeout = d
		} else {
			return fmt.Errorf("invalid READ timeout: %v", err)
		}
	}

	if writeTimeout := os.Getenv("WRITE_TIMEOUT"); writeTimeout != "" {
		if d, err := time.ParseDuration(writeTimeout); err == nil {
			c.WriteTimeout = d
		} else {
			return fmt.Errorf("invalid write timeout: %v", err)
		}
	}

	if shutdownTimeout := os.Getenv("SHUTDOWN_TIMEOUT"); shutdownTimeout != "" {
		if d, err := time.ParseDuration(shutdownTimeout); err == nil {
			c.ShutdownTimeout = d
		} else {
			return fmt.Errorf("invalid shutdown timeout: %v", err)
		}
	}

	if enableMetrics := os.Getenv("ENABLE_METRICS"); enableMetrics != "" {
		if b, err := strconv.ParseBool(enableMetrics); err == nil {
			c.EnableMetrics = b
		} else {
			return fmt.Errorf("invalid ENABLE_METRICS: %v", err)
		}
	}

	if enablePprof := os.Getenv("ENABLE_PPROF"); enablePprof != "" {
		if b, err := strconv.ParseBool(enablePprof); err == nil {
			c.EnablePprof = b
		} else {
			return fmt.Errorf("invalid ENABLE_PPROF: %v", err)
		}
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		c.LogLevel = logLevel
	}

	if logFormat := os.Getenv("LOG_FORMAT"); logFormat != "" {
		c.LogFormat = logFormat
	}

	if healthPath := os.Getenv("HEALTH_PATH"); healthPath != "" {
		c.HealthPath = healthPath
	}

	if readyPath := os.Getenv("READY_PATH"); readyPath != "" {
		c.ReadyPath = readyPath
	}

	if metricsPath := os.Getenv("METRICS_PATH"); metricsPath != "" {
		c.MetricsPath = metricsPath
	}

	if versionPath := os.Getenv("VERSION_PATH"); versionPath != "" {
		c.VersionPath = versionPath
	}

	return nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Port <= 0 || c.Port > 65535 {
		return fmt.Errorf("invalid port: %d", c.Port)
	}

	if c.MetricsPort <= 0 || c.MetricsPort > 65535 {
		return fmt.Errorf("invalid metrics port: %d", c.MetricsPort)
	}

	if c.Port == c.MetricsPort {
		return fmt.Errorf("port and metrics port cannot be the same: %d", c.Port)
	}

	if c.ReadTimeout <= 0 {
		return fmt.Errorf("read timeout must be positive: %v", c.ReadTimeout)
	}

	if c.WriteTimeout <= 0 {
		return fmt.Errorf("write timeout must be positive: %v", c.WriteTimeout)
	}

	if c.ShutdownTimeout <= 0 {
		return fmt.Errorf("shutdown timeout must be positive: %v", c.ShutdownTimeout)
	}

	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLogLevels[c.LogLevel] {
		return fmt.Errorf("invalid log level: %s", c.LogLevel)
	}

	validLogFormats := map[string]bool{
		"json": true,
		"text": true,
	}
	if !validLogFormats[c.LogFormat] {
		return fmt.Errorf("invalid log format: %s", c.LogFormat)
	}

	return nil
}
