/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package agent

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"sync/atomic"
	"time"
)

// Agent represents the synapse-agent server
type Agent struct {
	config  *Config
	logger  *Logger
	metrics *MetricsRecorder

	// Server instances
	mainServer    *http.Server
	metricsServer *http.Server

	// Health status
	healthy int64 // atomic boolean (1 = healthy, 0 = unhealthy)
	ready   int64 // atomic boolean (1 = ready, 0 = not ready)

	// Shutdown coordination
	shutdownOnce sync.Once
	shutdownCh   chan struct{}

	// Version info
	version *VersionInfo
}

// New creates a new Agent instance
func New(config *Config) (*Agent, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	logger := NewLogger(config)
	metrics := NewMetricsRecorder()
	version := GetVersionInfo()

	agent := &Agent{
		config:     config,
		logger:     logger,
		metrics:    metrics,
		healthy:    1, // Start healthy
		ready:      0, // Not ready until servers start
		shutdownCh: make(chan struct{}),
		version:    version,
	}

	// Set agent info metrics
	metrics.SetAgentInfo(version.Version, version.GoVersion, version.BuildDate)
	metrics.SetHealthStatus("liveness", true)
	metrics.SetHealthStatus("readiness", false)

	return agent, nil
}

// Start starts the agent servers
func (a *Agent) Start(ctx context.Context) error {
	a.logger.LogStartup(a.config, a.version.Version, a.version.BuildDate, a.version.GoVersion)

	// Setup main server
	mainMux := a.setupMainServer()
	a.mainServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", a.config.Port),
		Handler:      mainMux,
		ReadTimeout:  a.config.ReadTimeout,
		WriteTimeout: a.config.WriteTimeout,
	}

	// Setup metrics server if enabled
	if a.config.EnableMetrics {
		metricsMux := a.setupMetricsServer()
		a.metricsServer = &http.Server{
			Addr:         fmt.Sprintf(":%d", a.config.MetricsPort),
			Handler:      metricsMux,
			ReadTimeout:  a.config.ReadTimeout,
			WriteTimeout: a.config.WriteTimeout,
		}
	}

	// Start servers
	errCh := make(chan error, 2)

	// Start main server
	go func() {
		a.logger.LogServerStart("main", a.mainServer.Addr)
		if err := a.mainServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			errCh <- fmt.Errorf("main server failed: %w", err)
		}
	}()

	// Start metrics server if enabled
	if a.metricsServer != nil {
		go func() {
			a.logger.LogServerStart("metrics", a.metricsServer.Addr)
			if err := a.metricsServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				errCh <- fmt.Errorf("metrics server failed: %w", err)
			}
		}()
	}

	// Wait a moment for servers to start, then mark as ready
	go func() {
		time.Sleep(100 * time.Millisecond)
		atomic.StoreInt64(&a.ready, 1)
		a.metrics.SetHealthStatus("readiness", true)
		a.logger.Info("agent is ready")
	}()

	// Wait for shutdown signal or server error
	select {
	case err := <-errCh:
		return err
	case <-ctx.Done():
		return a.Shutdown(ctx)
	case <-a.shutdownCh:
		return a.Shutdown(ctx)
	}
}

// Shutdown gracefully shuts down the agent
func (a *Agent) Shutdown(ctx context.Context) error {
	var shutdownErr error
	a.shutdownOnce.Do(func() {
		start := time.Now()
		a.logger.Info("shutting down agent")

		// Mark as not ready
		atomic.StoreInt64(&a.ready, 0)
		a.metrics.SetHealthStatus("readiness", false)

		// Create shutdown context with timeout
		shutdownCtx, cancel := context.WithTimeout(ctx, a.config.ShutdownTimeout)
		defer cancel()

		// Shutdown servers
		var wg sync.WaitGroup

		if a.mainServer != nil {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if err := a.mainServer.Shutdown(shutdownCtx); err != nil {
					a.logger.LogError("main_server_shutdown", err)
					shutdownErr = err
				} else {
					a.logger.LogServerStop("main", time.Since(start))
				}
			}()
		}

		if a.metricsServer != nil {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if err := a.metricsServer.Shutdown(shutdownCtx); err != nil {
					a.logger.LogError("metrics_server_shutdown", err)
					shutdownErr = err
				} else {
					a.logger.LogServerStop("metrics", time.Since(start))
				}
			}()
		}

		wg.Wait()

		// Mark as unhealthy
		atomic.StoreInt64(&a.healthy, 0)
		a.metrics.SetHealthStatus("liveness", false)

		a.logger.LogShutdown("graceful", time.Since(start))
	})

	return shutdownErr
}

// Stop triggers a graceful shutdown
func (a *Agent) Stop() {
	select {
	case <-a.shutdownCh:
		// Already shutting down
	default:
		close(a.shutdownCh)
	}
}

// IsHealthy returns the health status
func (a *Agent) IsHealthy() bool {
	return atomic.LoadInt64(&a.healthy) == 1
}

// IsReady returns the readiness status
func (a *Agent) IsReady() bool {
	return atomic.LoadInt64(&a.ready) == 1
}

// GetVersion returns the version information
func (a *Agent) GetVersion() *VersionInfo {
	return a.version
}
