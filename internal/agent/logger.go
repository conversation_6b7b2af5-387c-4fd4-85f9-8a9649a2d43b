/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package agent

import (
	"log/slog"
	"os"
	"strings"
	"time"
)

// Logger wraps slog.Logger with additional functionality
type Logger struct {
	logger *slog.Logger
}

// NewLogger creates a new structured logger based on configuration
func NewLogger(config *Config) *Logger {
	var handler slog.Handler

	// Configure log level
	var level slog.Level
	switch strings.ToLower(config.LogLevel) {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{Level: level}

	// Configure log format
	switch strings.ToLower(config.LogFormat) {
	case "json":
		handler = slog.NewJSONHandler(os.Stdout, opts)
	case "text":
		handler = slog.NewTextHandler(os.Stdout, opts)
	default:
		handler = slog.NewJSONHandler(os.Stdout, opts)
	}

	logger := slog.New(handler)
	return &Logger{logger: logger}
}

// Info logs an info message
func (l *Logger) Info(msg string, args ...any) {
	l.logger.Info(msg, args...)
}

// Debug logs a debug message
func (l *Logger) Debug(msg string, args ...any) {
	l.logger.Debug(msg, args...)
}

// Warn logs a warning message
func (l *Logger) Warn(msg string, args ...any) {
	l.logger.Warn(msg, args...)
}

// Error logs an error message
func (l *Logger) Error(msg string, args ...any) {
	l.logger.Error(msg, args...)
}

// With returns a new logger with the given attributes
func (l *Logger) With(args ...any) *Logger {
	return &Logger{logger: l.logger.With(args...)}
}

// LogRequest logs an HTTP request with structured fields
func (l *Logger) LogRequest(method, path, remoteAddr, userAgent string, duration time.Duration, status int, responseSize int64) {
	l.logger.Info("request_handled",
		slog.String("method", method),
		slog.String("path", path),
		slog.String("remote_addr", remoteAddr),
		slog.String("user_agent", userAgent),
		slog.Duration("duration", duration),
		slog.Int("status", status),
		slog.Int64("response_size", responseSize),
	)
}

// LogStartup logs agent startup information
func (l *Logger) LogStartup(config *Config, version, buildDate, goVersion string) {
	l.logger.Info("agent_starting",
		slog.String("version", version),
		slog.String("build_date", buildDate),
		slog.String("go_version", goVersion),
		slog.Int("port", config.Port),
		slog.Int("metrics_port", config.MetricsPort),
		slog.Bool("metrics_enabled", config.EnableMetrics),
		slog.Bool("pprof_enabled", config.EnablePprof),
		slog.String("log_level", config.LogLevel),
		slog.String("log_format", config.LogFormat),
	)
}

// LogShutdown logs agent shutdown information
func (l *Logger) LogShutdown(reason string, duration time.Duration) {
	l.logger.Info("agent_shutdown",
		slog.String("reason", reason),
		slog.Duration("shutdown_duration", duration),
	)
}

// LogHealthCheck logs health check results
func (l *Logger) LogHealthCheck(checkType string, healthy bool, message string) {
	l.logger.Info("health_check",
		slog.String("check_type", checkType),
		slog.Bool("healthy", healthy),
		slog.String("message", message),
	)
}

// LogConfigLoad logs configuration loading
func (l *Logger) LogConfigLoad(source string, success bool, err error) {
	if success {
		l.logger.Info("config_loaded",
			slog.String("source", source),
			slog.Bool("success", success),
		)
	} else {
		l.logger.Error("config_load_failed",
			slog.String("source", source),
			slog.Bool("success", success),
			slog.String("error", err.Error()),
		)
	}
}

// LogServerStart logs server startup
func (l *Logger) LogServerStart(serverType string, addr string) {
	l.logger.Info("server_starting",
		slog.String("server_type", serverType),
		slog.String("address", addr),
	)
}

// LogServerStop logs server shutdown
func (l *Logger) LogServerStop(serverType string, duration time.Duration) {
	l.logger.Info("server_stopped",
		slog.String("server_type", serverType),
		slog.Duration("shutdown_duration", duration),
	)
}

// LogError logs an error with context
func (l *Logger) LogError(operation string, err error, args ...any) {
	allArgs := append([]any{
		slog.String("operation", operation),
		slog.String("error", err.Error()),
	}, args...)
	l.logger.Error("operation_failed", allArgs...)
}
