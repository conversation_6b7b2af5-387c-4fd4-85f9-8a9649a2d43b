/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package agent

import (
	"encoding/json"
	"runtime"
)

// Version information. These will be set at build time using ldflags
var (
	Version   = "dev"
	BuildDate = "unknown"
	GitCommit = "unknown"
)

// VersionInfo contains version and build information
type VersionInfo struct {
	Version   string `json:"version"`
	BuildDate string `json:"build_date"`
	GitCommit string `json:"git_commit"`
	GoVersion string `json:"go_version"`
	Platform  string `json:"platform"`
	Arch      string `json:"arch"`
}

// GetVersionInfo returns the version information
func GetVersionInfo() *VersionInfo {
	return &VersionInfo{
		Version:   Version,
		BuildDate: BuildDate,
		GitCommit: GitCommit,
		GoVersion: runtime.Version(),
		Platform:  runtime.GOOS,
		Arch:      runtime.GOARCH,
	}
}

// String returns a string representation of the version info
func (v *VersionInfo) String() string {
	return v.Version
}

// JSON returns a JSON representation of the version info
func (v *VersionInfo) JSON() ([]byte, error) {
	return json.Marshal(v)
}

// JSONPretty returns a pretty-printed JSON representation of the version info
func (v *VersionInfo) JSONPretty() ([]byte, error) {
	return json.MarshalIndent(v, "", "  ")
}
