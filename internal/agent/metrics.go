/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package agent

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Prometheus metrics for the synapse-agent
var (
	// requestsTotal counts the total number of HTTP requests received
	requestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "synapse_agent_requests_total",
			Help: "Total number of HTTP requests received by the agent",
		},
		[]string{"method", "path", "status"},
	)

	// requestDuration measures the duration of HTTP requests
	requestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "synapse_agent_request_duration_seconds",
			Help:    "Duration of HTTP requests in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "path"},
	)

	// activeConnections tracks the number of active connections
	activeConnections = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "synapse_agent_active_connections",
			Help: "Number of active connections to the agent",
		},
	)

	// agentInfo provides information about the agent
	agentInfo = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_agent_info",
			Help: "Information about the synapse agent",
		},
		[]string{"version", "go_version", "build_date"},
	)

	// healthStatus indicates the health status of the agent
	healthStatus = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "synapse_agent_health_status",
			Help: "Health status of the agent (1 = healthy, 0 = unhealthy)",
		},
		[]string{"check_type"},
	)

	// responseSize measures the size of HTTP responses
	responseSize = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "synapse_agent_response_size_bytes",
			Help:    "Size of HTTP responses in bytes",
			Buckets: []float64{10, 50, 100, 500, 1000, 5000, 10000},
		},
		[]string{"method", "path"},
	)
)

// MetricsRecorder provides methods to record metrics
type MetricsRecorder struct{}

// NewMetricsRecorder creates a new MetricsRecorder
func NewMetricsRecorder() *MetricsRecorder {
	return &MetricsRecorder{}
}

// RecordRequest records metrics for an HTTP request
func (m *MetricsRecorder) RecordRequest(method, path, status string, duration float64, respSize float64) {
	requestsTotal.WithLabelValues(method, path, status).Inc()
	requestDuration.WithLabelValues(method, path).Observe(duration)
	responseSize.WithLabelValues(method, path).Observe(respSize)
}

// SetActiveConnections sets the number of active connections
func (m *MetricsRecorder) SetActiveConnections(count float64) {
	activeConnections.Set(count)
}

// IncActiveConnections increments the active connections counter
func (m *MetricsRecorder) IncActiveConnections() {
	activeConnections.Inc()
}

// DecActiveConnections decrements the active connections counter
func (m *MetricsRecorder) DecActiveConnections() {
	activeConnections.Dec()
}

// SetAgentInfo sets the agent information
func (m *MetricsRecorder) SetAgentInfo(version, goVersion, buildDate string) {
	agentInfo.WithLabelValues(version, goVersion, buildDate).Set(1)
}

// SetHealthStatus sets the health status for a specific check type
func (m *MetricsRecorder) SetHealthStatus(checkType string, healthy bool) {
	value := float64(0)
	if healthy {
		value = 1
	}
	healthStatus.WithLabelValues(checkType).Set(value)
}

// ResetMetrics resets all metrics (useful for testing)
func (m *MetricsRecorder) ResetMetrics() {
	requestsTotal.Reset()
	requestDuration.Reset()
	activeConnections.Set(0)
	agentInfo.Reset()
	healthStatus.Reset()
	responseSize.Reset()
}
