/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package agent

import (
	"bufio"
	"fmt"
	"net"
	"net/http"
	"net/http/pprof"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// setupMainServer configures the main HTTP server with all endpoints
func (a *Agent) setupMainServer() *http.ServeMux {
	mux := http.NewServeMux()

	// Main probe endpoint - exact match only
	mux.HandleFunc("GET /", a.withMetrics(a.probeHandler))

	// Health check endpoints
	mux.HandleFunc("GET "+a.config.HealthPath, a.withMetrics(a.healthHandler))
	mux.HandleFunc("GET "+a.config.ReadyPath, a.withMetrics(a.readyHandler))

	// Version endpoint
	mux.HandleFunc("GET "+a.config.VersionPath, a.withMetrics(a.versionHandler))

	// Pprof endpoints if enabled
	if a.config.EnablePprof {
		mux.HandleFunc("/debug/pprof/", pprof.Index)
		mux.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
		mux.HandleFunc("/debug/pprof/profile", pprof.Profile)
		mux.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
		mux.HandleFunc("/debug/pprof/trace", pprof.Trace)
	}

	// Catch-all handler for 404s
	mux.HandleFunc("/", a.withMetrics(a.notFoundHandler))

	return mux
}

// setupMetricsServer configures the metrics HTTP server
func (a *Agent) setupMetricsServer() *http.ServeMux {
	mux := http.NewServeMux()

	// Prometheus metrics endpoint
	mux.Handle(a.config.MetricsPath, promhttp.Handler())

	// Health endpoints on metrics server too
	mux.HandleFunc("/health", a.healthHandler)
	mux.HandleFunc("/ready", a.readyHandler)

	return mux
}

// probeHandler handles the main probe endpoint - optimized for speed
func (a *Agent) probeHandler(w http.ResponseWriter, r *http.Request) {
	// Only handle exact root path
	if r.URL.Path != "/" {
		a.notFoundHandler(w, r)
		return
	}
	// Immediately respond with 200 OK for minimal latency
	w.WriteHeader(http.StatusOK)
	// Empty body as per design specification
}

// notFoundHandler handles requests to unknown endpoints
func (a *Agent) notFoundHandler(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotFound)
	w.Write([]byte("Not Found"))
}

// healthHandler handles liveness probe requests
func (a *Agent) healthHandler(w http.ResponseWriter, r *http.Request) {
	if a.IsHealthy() {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte("Service Unavailable"))
	}
}

// readyHandler handles readiness probe requests
func (a *Agent) readyHandler(w http.ResponseWriter, r *http.Request) {
	if a.IsReady() {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Ready"))
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte("Not Ready"))
	}
}

// versionHandler handles version information requests
func (a *Agent) versionHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	version := a.GetVersion()
	data, err := version.JSONPretty()
	if err != nil {
		a.logger.LogError("version_marshal", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write(data)
}

// withMetrics wraps an HTTP handler with metrics collection
func (a *Agent) withMetrics(handler http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if !a.config.EnableMetrics {
			handler(w, r)
			return
		}

		start := time.Now()
		a.metrics.IncActiveConnections()
		defer a.metrics.DecActiveConnections()

		// Wrap ResponseWriter to capture status and size
		wrapped := &responseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
		}

		// Call the actual handler
		handler(wrapped, r)

		// Record metrics
		duration := time.Since(start).Seconds()
		status := strconv.Itoa(wrapped.statusCode)
		responseSize := float64(wrapped.size)

		a.metrics.RecordRequest(r.Method, r.URL.Path, status, duration, responseSize)

		// Log request if not the main probe endpoint (to avoid spam)
		if r.URL.Path != "/" {
			a.logger.LogRequest(
				r.Method,
				r.URL.Path,
				r.RemoteAddr,
				r.UserAgent(),
				time.Since(start),
				wrapped.statusCode,
				int64(wrapped.size),
			)
		}
	}
}

// responseWriter wraps http.ResponseWriter to capture response metrics
type responseWriter struct {
	http.ResponseWriter
	statusCode int
	size       int
}

// WriteHeader captures the status code
func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// Write captures the response size
func (rw *responseWriter) Write(b []byte) (int, error) {
	size, err := rw.ResponseWriter.Write(b)
	rw.size += size
	return size, err
}

// Hijack implements http.Hijacker interface
func (rw *responseWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	if hijacker, ok := rw.ResponseWriter.(http.Hijacker); ok {
		return hijacker.Hijack()
	}
	return nil, nil, fmt.Errorf("hijacking not supported")
}
