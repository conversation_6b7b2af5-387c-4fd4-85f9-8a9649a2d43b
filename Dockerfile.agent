# Build stage
FROM golang:1.21-alpine AS builder

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates git

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd/agent/ ./cmd/agent/
COPY internal/agent/ ./internal/agent/

# Build arguments for version information
ARG VERSION=dev
ARG BUILD_DATE=unknown
ARG GIT_COMMIT=unknown

# Build the binary
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -a -installsuffix cgo \
    -ldflags="-w -s -X 'code.devops.xiaohongshu.com/cloud-native/synapse/internal/agent.Version=${VERSION}' -X 'code.devops.xiaohongshu.com/cloud-native/synapse/internal/agent.BuildDate=${BUILD_DATE}' -X 'code.devops.xiaohongshu.com/cloud-native/synapse/internal/agent.GitCommit=${GIT_COMMIT}'" \
    -o synapse-agent \
    ./cmd/agent

# Final stage
FROM gcr.io/distroless/static-debian11:nonroot

# Copy ca-certificates from builder
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary
COPY --from=builder /app/synapse-agent /synapse-agent

# Expose ports
EXPOSE 8080 8081

# Use non-root user
USER nonroot:nonroot

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/synapse-agent", "--health-check"] || exit 1

# Run the binary
ENTRYPOINT ["/synapse-agent"]
