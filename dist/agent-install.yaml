apiVersion: v1
kind: Namespace
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
  labels:
    app.kubernetes.io/component: monitoring
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
    name: synapse-monitoring
  name: synapse-monitoring
---
apiVersion: v1
automountServiceAccountToken: false
kind: ServiceAccount
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
  labels:
    app: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  name: synapse-agent
  namespace: synapse-monitoring
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
  labels:
    app: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  name: synapse-agent
rules:
- apiGroups:
  - ""
  resourceNames: []
  resources:
  - nodes
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
  labels:
    app: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  name: synapse-agent
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: synapse-agent
subjects:
- kind: ServiceAccount
  name: synapse-agent
  namespace: synapse-monitoring
---
apiVersion: v1
data:
  ENABLE_METRICS: "true"
  ENABLE_PPROF: "false"
  HEALTH_PATH: /health
  LISTEN_PORT: "8080"
  LOG_FORMAT: json
  LOG_LEVEL: info
  METRICS_PATH: /metrics
  METRICS_PORT: "8081"
  READ_TIMEOUT: 5s
  READY_PATH: /ready
  SHUTDOWN_TIMEOUT: 30s
  VERSION_PATH: /version
  WRITE_TIMEOUT: 10s
kind: ConfigMap
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
  labels:
    app: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  name: synapse-agent-config
  namespace: synapse-monitoring
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
    prometheus.io/path: /metrics
    prometheus.io/port: "8081"
    prometheus.io/scrape: "true"
  labels:
    app: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  name: synapse-agent-metrics
  namespace: synapse-monitoring
spec:
  clusterIP: None
  ports:
  - name: metrics
    port: 8081
    protocol: TCP
    targetPort: metrics
  - name: http
    port: 8080
    protocol: TCP
    targetPort: http
  selector:
    app: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  type: ClusterIP
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
  labels:
    app: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  name: synapse-agent
  namespace: synapse-monitoring
spec:
  selector:
    matchLabels:
      app: synapse-agent
      app.kubernetes.io/part-of: synapse
      app.kubernetes.io/version: v1.0.0
  template:
    metadata:
      annotations:
        app.kubernetes.io/managed-by: kustomize
        prometheus.io/path: /metrics
        prometheus.io/port: "8081"
        prometheus.io/scrape: "true"
      labels:
        app: synapse-agent
        app.kubernetes.io/component: agent
        app.kubernetes.io/name: synapse-agent
        app.kubernetes.io/part-of: synapse
        app.kubernetes.io/version: v1.0.0
    spec:
      containers:
      - env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        envFrom:
        - configMapRef:
            name: synapse-agent-config
        image: synapse-agent:latest
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
        name: agent
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 8081
          name: metrics
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
        resources:
          limits:
            cpu: 50m
            memory: 32Mi
          requests:
            cpu: 10m
            memory: 16Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
        volumeMounts:
        - mountPath: /tmp
          name: tmp
      nodeSelector:
        kubernetes.io/os: linux
      securityContext:
        fsGroup: 65532
        runAsGroup: 65532
        runAsNonRoot: true
        runAsUser: 65532
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: synapse-agent
      terminationGracePeriodSeconds: 30
      tolerations:
      - effect: NoSchedule
        operator: Exists
      - effect: NoExecute
        operator: Exists
      volumes:
      - emptyDir: {}
        name: tmp
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  annotations:
    app.kubernetes.io/managed-by: kustomize
  labels:
    app: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/part-of: synapse
    app.kubernetes.io/version: v1.0.0
  name: synapse-agent-netpol
  namespace: synapse-monitoring
spec:
  egress:
  - ports:
    - port: 53
      protocol: UDP
    - port: 53
      protocol: TCP
    to: []
  - ports:
    - port: 443
      protocol: TCP
    to: []
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: controller-system
      podSelector:
        matchLabels:
          app.kubernetes.io/part-of: synapse
          app.kubernetes.io/version: v1.0.0
          control-plane: controller-manager
    ports:
    - port: 8080
      protocol: TCP
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: prometheus
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - port: 8081
      protocol: TCP
  - from: []
    ports:
    - port: 8080
      protocol: TCP
  podSelector:
    matchLabels:
      app: synapse-agent
      app.kubernetes.io/part-of: synapse
      app.kubernetes.io/version: v1.0.0
  policyTypes:
  - Ingress
  - Egress
