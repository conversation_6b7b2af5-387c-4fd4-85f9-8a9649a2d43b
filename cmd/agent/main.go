package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
)

// pingHandler responds to any request with a simple 200 OK.
func pingHandler(w http.ResponseWriter, r *http.Request) {
	// As per the design, we do not need to write any body.
	// The 200 OK status is sent implicitly.
	w.<PERSON><PERSON>ead<PERSON>(http.StatusOK)
}

func main() {
	http.HandleFunc("/", pingHandler)

	port := os.Getenv("LISTEN_PORT")
	if port == "" {
		port = "8080" // Default port if not specified.
	}

	log.Printf("Starting synapse-agent on port %s", port)
	if err := http.ListenAndServe(fmt.Sprintf(":%s", port), nil); err != nil {
		log.Fatalf("Error starting server: %s", err)
	}
}
