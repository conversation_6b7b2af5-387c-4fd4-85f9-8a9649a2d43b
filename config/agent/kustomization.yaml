apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  annotations:
    config.kubernetes.io/local-config: "true"
  name: synapse-agent

resources:
- namespace.yaml
- configmap.yaml
- rbac.yaml
- service.yaml
- daemonset.yaml
- networkpolicy.yaml

commonLabels:
  app.kubernetes.io/part-of: synapse
  app.kubernetes.io/version: v1.0.0

images:
- name: synapse-agent
  newName: synapse-agent
  newTag: latest

# Namespace for all resources
namespace: synapse-monitoring

# Add common annotations
commonAnnotations:
  app.kubernetes.io/managed-by: kustomize
  
# Patches can be added here for environment-specific customizations

# ConfigMap generator for additional config files if needed

# Secret generator for sensitive data if needed
