apiVersion: v1
kind: Service
metadata:
  name: synapse-agent-metrics
  namespace: synapse-monitoring
  labels:
    app: synapse-agent
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8081"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  clusterIP: None  # Headless service for metrics scraping
  selector:
    app: synapse-agent
  ports:
  - name: metrics
    port: 8081
    targetPort: metrics
    protocol: TCP
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
