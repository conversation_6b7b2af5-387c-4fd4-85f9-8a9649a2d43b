apiVersion: v1
kind: ServiceAccount
metadata:
  name: synapse-agent
  namespace: synapse-monitoring
  labels:
    app: synapse-agent
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
automountServiceAccountToken: false
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: synapse-agent
  labels:
    app: synapse-agent
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
rules:
# Currently, the agent doesn't need any special permissions
# It's a simple HTTP endpoint that responds to probes
# This ClusterRole is created for future extensibility
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get"]
  resourceNames: [] # Can be restricted to specific nodes if needed
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: synapse-agent
  labels:
    app: synapse-agent
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: synapse-agent
subjects:
- kind: ServiceAccount
  name: synapse-agent
  namespace: synapse-monitoring
