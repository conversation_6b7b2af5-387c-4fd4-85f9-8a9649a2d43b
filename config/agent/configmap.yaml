apiVersion: v1
kind: ConfigMap
metadata:
  name: synapse-agent-config
  namespace: synapse-monitoring
  labels:
    app: synapse-agent
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
data:
  # Server configuration
  LISTEN_PORT: "8080"
  METRICS_PORT: "8081"
  READ_TIMEOUT: "5s"
  WRITE_TIMEOUT: "10s"
  SHUTDOWN_TIMEOUT: "30s"
  
  # Feature flags
  ENABLE_METRICS: "true"
  ENABLE_PPROF: "false"
  
  # Logging configuration
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  
  # Health check paths
  HEALTH_PATH: "/health"
  READY_PATH: "/ready"
  METRICS_PATH: "/metrics"
  VERSION_PATH: "/version"
