apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: synapse-agent-metrics
  namespace: synapse-monitoring
  labels:
    app: synapse-agent
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
spec:
  selector:
    matchLabels:
      app: synapse-agent
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    honorLabels: true
    relabelings:
    - sourceLabels: [__meta_kubernetes_pod_node_name]
      targetLabel: node
    - sourceLabels: [__meta_kubernetes_pod_name]
      targetLabel: pod
    - sourceLabels: [__meta_kubernetes_namespace]
      targetLabel: namespace
  namespaceSelector:
    matchNames:
    - synapse-monitoring
