apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: synapse-agent-netpol
  namespace: synapse-monitoring
  labels:
    app: synapse-agent
    app.kubernetes.io/name: synapse-agent
    app.kubernetes.io/component: agent
    app.kubernetes.io/managed-by: kustomize
spec:
  podSelector:
    matchLabels:
      app: synapse-agent
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from synapse-controller for probing
  - from:
    - namespaceSelector:
        matchLabels:
          name: controller-system
      podSelector:
        matchLabels:
          control-plane: controller-manager
    ports:
    - protocol: TCP
      port: 8080
  # Allow metrics scraping from monitoring systems
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: prometheus
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 8081
  # Allow health checks from kubelet
  - from: []
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS for potential future needs (e.g., webhook calls)
  - to: []
    ports:
    - protocol: TCP
      port: 443
